{"name": "@trendency/kesma-core", "version": "3.5.2", "license": "SEE LICENSE IN LICENSE.txt", "scripts": {"cli:use-env": "node ./src/lib/cli/use-env.js"}, "bin": {"kesma-use-env": "./src/cli/use-env.js"}, "peerDependencies": {"@angular/common": ">=19.2.1", "@angular/core": ">=19.2.1", "date-fns": ">=4.1.0", "date-fns-tz": ">=3.2.0", "rxjs": ">=7.8.2", "zod": "^3.24.2"}, "dependencies": {"tslib": "^2.8.1"}, "publishConfig": {"registry": "http://dev-nexus.trendency.hu/repository/npm-hosted/"}}