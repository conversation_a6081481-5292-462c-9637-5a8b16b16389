#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const args = process.argv.slice(2);
let env = null;
let rawFile = null;

args.forEach((arg, index) => {
  if (arg === '--raw') {
    rawFile = args[index + 1];
  } else if (!arg.startsWith('--')) {
    env = arg;
  }
});

const ENV_FOLDER = path.resolve(process.cwd(), 'src/environments');
const DEST_FOLDER = path.resolve(process.cwd(), 'dist/browser');

let sourcePath;
let envFilename;

if (rawFile) {
  sourcePath = path.resolve(ENV_FOLDER, rawFile);
  envFilename = rawFile;
} else {
  envFilename = env ? `environment.${env}.json` : 'environment.json';
  sourcePath = path.join(ENV_FOLDER, envFilename);
}

const destPath = path.join(DEST_FOLDER, 'environment.json');

function copyEnvironmentFile() {
  if (!fs.existsSync(sourcePath)) {
    console.error(`❌ Environment file '${envFilename}' not found in '${ENV_FOLDER}'`);
    process.exit(1);
  }

  if (!fs.existsSync(DEST_FOLDER)) {
    console.error(`❌ Destination folder '${DEST_FOLDER}' not found. You need to first build the project in order to use this command.`);
    process.exit(1);
  }

  fs.copyFileSync(sourcePath, destPath);
  console.log(`✅ Copied '${envFilename}' to '${destPath}' as 'environment.json'`);
}
copyEnvironmentFile();
