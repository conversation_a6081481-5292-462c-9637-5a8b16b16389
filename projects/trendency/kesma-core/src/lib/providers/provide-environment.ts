import { ApplicationConfig, Provider } from '@angular/core';
import { Environment } from '../definitions';
import { AppEnvironment } from '../tokens';

export const provideEnvironment = (environment: Environment): Provider => {
  return {
    provide: AppEnvironment,
    useValue: environment,
  };
};

export const provideAppConfigWithEnvironment = (appConfig: ApplicationConfig, environment: Environment): ApplicationConfig => {
  return {
    ...appConfig,
    providers: [provideEnvironment(environment), ...appConfig.providers],
  };
};
