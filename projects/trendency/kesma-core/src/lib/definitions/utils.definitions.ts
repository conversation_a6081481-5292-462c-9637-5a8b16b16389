import { HttpHeaders, HttpParams } from '@angular/common/http';

export type BrowserType = 'opera' | 'firefox' | 'safari' | 'ie' | 'edge' | 'chrome' | 'edge-chromium';

export type LogType = 'info' | 'success' | 'warning' | 'error';

export interface IHttpOptions {
  readonly headers?:
    | HttpHeaders
    | {
        readonly [header: string]: string | readonly string[];
      };
  readonly observe?: any;
  readonly params?:
    | HttpParams
    | {
        readonly [param: string]: string | readonly string[];
      };
  readonly reportProgress?: boolean;
  readonly responseType?: any;
  readonly withCredentials?: boolean;
}

export interface IMetaData {
  readonly title?: string;
  readonly canonicalUrl?: string;
  readonly description?: string;
  readonly keywords?: string;
  readonly robots?: string;
  readonly ogAppId?: string;
  readonly ogTitle?: string;
  readonly ogUrl?: string;
  readonly ogImage?: string;
  readonly ogImageWidth?: string;
  readonly ogImageHeight?: string;
  readonly ogType?: string;
  readonly ogDescription?: string;
  readonly ogLocale?: string;
  readonly ogSiteName?: string;
  readonly articlePublishedTime?: string;
  readonly articleModifiedTime?: string;
  readonly articleAuthor?: string;
  readonly author?: string;
  readonly twitterSiteName?: string;
  readonly twitterTitle?: string;
  readonly twitterDescription?: string;
  readonly twitterImage?: string;
}

export type DateFormat =
  | 'full-day'
  | 'month'
  | 'year'
  | 'date'
  | 'h-m-s'
  | 'h-m'
  | 'l-d'
  | 'l-d-e'
  | 'l-d-h-m'
  | 'y-l-d-h-m'
  | 'y-l-m-d-h-m'
  | 'dateTime'
  | 'hungarian'
  | 'hungarianLong';
