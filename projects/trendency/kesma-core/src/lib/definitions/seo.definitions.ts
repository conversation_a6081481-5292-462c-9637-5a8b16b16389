export type SeoMeta = {
  title?: string;
  description?: string;
  canonical?: string;
  robots?: string[];
};

export type SeoMetaDataParams = {
  /**
   * Skips the seo meta check. This is useful when you want to set the metadata manually and avoid checking the API.
   * @default false
   */
  skipSeoMetaCheck?: boolean;
  /**
   * Skips the robots fallback. This is useful when you want to set the robots metadata to empty.
   * @default false
   */
  canRobotsBeEmpty?: boolean;
};

export type SeoCanonicalDataParams = {
  /**
   * Skips the seo meta check. This is useful when you want to set the meta data manually and avoid checking the API.
   * @default false
   */
  skipSeoMetaCheck?: boolean;

  /**
   * Adds the host url before the provided url. This is useful for relative urls.
   */
  addHostUrl?: boolean;
};
