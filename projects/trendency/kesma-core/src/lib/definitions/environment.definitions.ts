import { z } from 'zod';

// apiUrl:
// - Az az url amit a böngésző használ az API hívásokhoz böngésző és szerver oldalon
// vagy
//  apiUrl.clientApiUrl:
//    - Az az url amit a böngésző használ az API hívásokhoz
//  apiUrl.serverApiUrl:
//    - Az az url amit SSR-nél a Node használ az API hívásokhoz
// translation:
//  - locales: az oldalon használt nyelvek listája
//  - prefix: a route fordítókulcsok prefixe
//  - localesFolder:
//     - client: a mappa elérési útja böngésző oldalon, ahol a fordításhoz használt {lang}.JSON fájlok találhatók.
//     - server: a mappa elérési útja Node oldalon, ahol a fordításhoz használt {lang}.JSON fájlok találhatók.
// redis:
// - redisIp: a Redis szerver IP címe
// - redisPort: a port amin fut a Redis
// - redisPrefix: milyen prefix-szel kerüljenek a db-be a kulcsok
// - redisRequestLimit: hány requestet engedélyezzen IP címenként
// withHttps: Ha true, akkor a Node HTTPS-el fog futni, self-signed certificate-el

export const EnvironmentDedicatedApiUrlSchema = z.object({
  clientApiUrl: z.string(),
  serverApiUrl: z.string(),
});

export const SSRProxyConfigSchema = z.object({
  path: z.string(),
  target: z.string(),
});

export const EnvironmentSchema = z.object({
  production: z.boolean(),
  type: z.string(),
  apiUrl: z.union([EnvironmentDedicatedApiUrlSchema, z.string()]).optional(),
  secureApiUrl: z
    .union([
      z.object({
        clientApiUrl: z.string(),
        serverApiUrl: z.string(),
      }),
      z.string(),
    ])
    .optional(),
  personalizedRecommendationApiUrl: z
    .union([
      z.object({
        clientApiUrl: z.string(),
        serverApiUrl: z.string(),
      }),
      z.string(),
    ])
    .optional(),
  redis: z
    .object({
      redisIp: z.string(),
      redisPort: z.number(),
      redisPrefix: z.string(),
      redisRequestLimit: z.number(),
    })
    .optional(),
  withHttps: z.boolean().optional(),
  facebookAppId: z.string().optional(),
  googleClientId: z.string().optional(),
  appleClientId: z.string().optional(),
  googleSiteKey: z.string().optional(),
  googleTagManager: z.string().optional(),
  gemiusId: z.string().optional(),
  siteUrl: z.string().optional(),
  siteName: z.string().optional(),
  httpReqTimeout: z.number().optional(),
  sentry: z
    .object({
      dsn: z.string(),
      tracingOrigins: z.array(z.string()).optional(),
      sampleRate: z.number().optional(),
      tracesSampleRate: z.number().optional(),
      profilesSampleRate: z.number().optional(),
      integrations: z.any().optional(),
    })
    .optional(),
  ssrProxyConfig: z.array(SSRProxyConfigSchema).optional(),
  iriszApiUrl: z
    .union([
      z.object({
        clientApiUrl: z.string(),
        serverApiUrl: z.string(),
      }),
      z.string(),
    ])
    .optional(),
});

export type EnvironmentType = z.infer<typeof EnvironmentSchema>['type'];

export type Environment = z.infer<typeof EnvironmentSchema>;

export type EnvironmentApiUrl = z.infer<typeof EnvironmentDedicatedApiUrlSchema>;

export type EnvironmentRedisConfig = z.infer<typeof EnvironmentSchema>['redis'];

export type EnvironmentSentryConfig = z.infer<typeof EnvironmentSchema>['sentry'];

export type SSRProxyConfig = z.infer<typeof SSRProxyConfigSchema>;
