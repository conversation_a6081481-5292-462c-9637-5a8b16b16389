import { Directive, HostListener, Inject, OnDestroy, OnInit } from '@angular/core';
import { DOCUMENT } from '@angular/common';

@Directive({
  selector: '[trFullscreen]',
  exportAs: 'trFullscreen',
})
export class FullscreenDirective implements OnInit, OnDestroy {
  elem?: HTMLElement;
  isFullScreen?: boolean;

  constructor(@Inject(DOCUMENT) private readonly document: Document) {}

  ngOnInit(): void {
    this.checkScreenMode();
    this.elem = this.document.documentElement;
  }

  @HostListener('document:fullscreenchange', ['$event'])
  @HostListener('document:webkitfullscreenchange', ['$event'])
  fullscreenmodes(event: Event) {
    this.checkScreenMode();
  }

  checkScreenMode() {
    this.isFullScreen = !!this.document.fullscreenElement;
  }

  toggleFullScreen() {
    if (!this.isFullScreen) {
      this.openFullscreen();
    } else {
      this.closeFullscreen();
    }
  }

  openFullscreen() {
    if (this.elem?.requestFullscreen) {
      this.elem.requestFullscreen();
    }
  }

  closeFullscreen() {
    if (this.document.exitFullscreen && this.isFullScreen) {
      this.document.exitFullscreen();
    }
  }

  ngOnDestroy(): void {
    this.closeFullscreen();
  }
}
