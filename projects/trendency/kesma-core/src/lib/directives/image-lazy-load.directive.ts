import { AfterViewInit, Directive, ElementRef, EventEmitter, HostBinding, Inject, Input, Output } from '@angular/core';
import { UtilService } from '../services';
import { canLazyLoad, isVisible } from '../utils';
import { Environment } from '../definitions';
import { AppEnvironment } from '../tokens';

/**
 * @deprecated old portals use only
 */
@Directive({
  selector: '[trImageLazyLoad]',
})
export class ImageLazyLoadDirective implements AfterViewInit {
  @Output() public intersect: EventEmitter<boolean> = new EventEmitter<boolean>(false);

  @Input() src?: string;
  @Input() backgroundImageSrc?: string;
  @Input() trImageLazyLoad = 'image-loader.gif';
  @HostBinding('attr.src') srcAttr?: string | null;
  @HostBinding('attr.loading') imageLoading?: string;

  @Input()
  set trendImageLazyLoadPlaceholder(value: string) {
    this.srcAttr = value;
  }

  get placeholderImage() {
    return '/assets/images/' + this.trImageLazyLoad;
  }

  private imgSrc?: string;

  constructor(
    @Inject(AppEnvironment) private readonly environment: Environment,
    private readonly el: ElementRef<HTMLElement>,
    private readonly utilsService: UtilService
  ) {}

  ngAfterViewInit() {
    if (this.el?.nativeElement?.tagName === 'IMG') {
      this.el.nativeElement.setAttribute('src', this.src ?? '');
      this.el.nativeElement.setAttribute('loading', 'lazy');
      return;
    }
    // Force hiding of images on SSR -> otherwise Google detects the src and downgrades site
    if (this.el?.nativeElement && (!this.utilsService.isBrowser() || (canLazyLoad && !isVisible(this.el.nativeElement)))) {
      this.getOriginalImage();
      if (this.utilsService.isBrowser()) {
        this.loadPlaceholder();
        this.lazyLoadImage();
      }
    } else {
      if (this.utilsService.isBrowser() && !this.environment.production) {
        this.utilsService.log('ImageLazyLoadDirective: Nem lehet lazy load-olni, mert a böngésző nem támogatja az IntersectionObserver-t!', 'warning');
      }
    }
  }

  private lazyLoadImage() {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(({ isIntersecting }) => {
        if (isIntersecting) {
          this.intersect.emit(true);
          this.loadImage();
          observer.unobserve(this.el.nativeElement);
        }
      });
    });
    observer.observe(this.el.nativeElement);
  }

  private loadImage() {
    if (this.imgSrc) {
      this.srcAttr = this.imgSrc;
    } else if (this.backgroundImageSrc) {
      this.el.nativeElement.style.backgroundImage = this.backgroundImageSrc;
    }
  }

  private loadPlaceholder() {
    if (!this.utilsService.isBrowser()) {
      // Prevent placeholder on SSR -> image url is detected and downgrades site
      return;
    }

    if (this.imgSrc) {
      this.srcAttr = this.placeholderImage;
    } else if (this.backgroundImageSrc) {
      this.el.nativeElement.style.backgroundImage = `url(${this.placeholderImage})`;
    }
  }

  private getOriginalImage() {
    if (this.src) {
      this.imgSrc = this.src;
      this.srcAttr = null;
    } else if (this.el.nativeElement.style.backgroundImage) {
      this.backgroundImageSrc = this.el.nativeElement.style.backgroundImage;
      this.el.nativeElement.style.backgroundImage = 'none';
    }
  }
}
