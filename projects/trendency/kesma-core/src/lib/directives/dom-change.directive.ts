import { Directive, ElementRef, EventEmitter, OnDestroy, Output } from '@angular/core';
import { UtilService } from '../services';

@Directive({
  selector: '[trDomChange]',
})
export class DomChangeDirective implements OnDestroy {
  private readonly changes?: MutationObserver;

  @Output()
  public domChange = new EventEmitter();

  constructor(
    private elementRef: ElementRef,
    private readonly utilsService: UtilService
  ) {
    const element = this.elementRef.nativeElement;

    if (!this.utilsService.isBrowser()) {
      return;
    }

    this.changes = new MutationObserver((mutations: MutationRecord[]) => {
      mutations.forEach((mutation: MutationRecord) => this.domChange.emit(mutation));
    });

    this.changes.observe(element, {
      childList: true,
    });
  }

  ngOnDestroy(): void {
    if (this.changes) {
      this.changes.disconnect();
    }
  }
}
