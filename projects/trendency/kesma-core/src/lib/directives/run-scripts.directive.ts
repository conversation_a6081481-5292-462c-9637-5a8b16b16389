import { Directive, ElementRef, Inject, Input, OnInit, PLATFORM_ID } from '@angular/core';
import { DOCUMENT, isPlatformServer } from '@angular/common';
import { UtilService } from '../services';

@Directive({
  selector: '[trRunScripts]',
})
export class RunScriptsDirective implements OnInit {
  @Input() async = false;
  @Input() defer = false;

  /**
   * The script tags will be left unchanged on SSR.
   */
  @Input() skipOnSsr = false;
  /**
   * By enabling this feature, the script tags will be removed from the page rendered by the SSR.
   * This could be useful for scenarios where it is problematic to render the script tags in the server response.
   */
  @Input() removeScriptsOnSsr = false;

  constructor(
    private elementRef: ElementRef,
    private readonly utilsService: UtilService,
    @Inject(PLATFORM_ID) private readonly platformId: Object,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  ngOnInit(): void {
    if (this.utilsService.isBrowser()) {
      setTimeout(() => {
        // wait for DOM rendering
        this.reinsertScripts();
      });
    } else {
      this.reinsertScripts();
    }
  }

  reinsertScripts(): void {
    const scripts = this.elementRef.nativeElement.getElementsByTagName('script') as HTMLScriptElement[];

    Array.from(scripts || []).forEach((script) => {
      if ((this.removeScriptsOnSsr || this.skipOnSsr) && isPlatformServer(this.platformId)) {
        if (this.removeScriptsOnSsr) {
          script.remove();
        }
        return;
      }
      const scriptCopy = this.document.createElement('script') as HTMLScriptElement;
      this.cloneAttributes(scriptCopy, script);

      scriptCopy.type = script.type ? script.type : 'text/javascript';
      if (script.innerHTML) {
        scriptCopy.innerHTML = script.innerHTML;
      } else if (script.src) {
        scriptCopy.src = script.src;
      }

      scriptCopy.async = this.async;
      scriptCopy.defer = this.defer;

      if (script.onload) {
        scriptCopy.onload = script.onload;
      }
      script.parentNode?.replaceChild(scriptCopy, script);
    });
  }

  cloneAttributes(target: HTMLScriptElement, source: any) {
    if (!source || !('attributes' in source)) return;

    [...source.attributes].forEach((attr) => {
      target.setAttribute(attr.nodeName, attr.nodeValue);
    });
  }
}
