import { AfterViewInit, Directive, ElementRef, Inject, Input } from '@angular/core';
import { UtilService } from '../services';
import { DOCUMENT } from '@angular/common';

@Directive({
  selector: '[trYouTubeLazyLoad]',
})
export class YouTubeLazyLoadDirective implements AfterViewInit {
  private src?: string;

  constructor(
    private readonly el: ElementRef,
    private readonly utilsService: UtilService,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  @Input()
  set youtubeSrc(src: string) {
    this.src = src;
  }

  ngAfterViewInit() {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    this.src = `${this.utilsService.convertYouTubeLinkToEmbed(this.src ?? '')}?autoplay=1`;
    const id = this.src?.split('embed/')[1] && this.src?.split('embed/')[1].substring(0, 11);
    if (!id) {
      return;
    }
    const thumbnailImage = `http://img.youtube.com/vi/${id}/sddefault.jpg`;
    const image = new Image();
    image.src = thumbnailImage;
    image.style.maxWidth = window.getComputedStyle(this.el.nativeElement).getPropertyValue('width');
    image.style.maxHeight = window.getComputedStyle(this.el.nativeElement).getPropertyValue('height');
    image.onload = () => {
      this.el.nativeElement.appendChild(image);
      this.el.nativeElement.classList.add('tr-iframe-loader-placeholder');
    };
    this.el.nativeElement.addEventListener('click', () => this.createIframe());
  }

  private createIframe() {
    this.el.nativeElement.classList.remove('tr-iframe-loader-placeholder');
    const iframe = this.document.createElement('iframe');
    iframe.allowFullscreen = true;
    iframe.style.width = '100%';
    iframe.style.height = '100%';
    iframe.src = this.src ?? '';
    this.el.nativeElement.innerHTML = '';
    this.el.nativeElement.appendChild(iframe);
  }
}
