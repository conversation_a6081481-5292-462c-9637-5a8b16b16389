import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

import { UtilService } from './util.service';

@Injectable({
  providedIn: 'root',
})
export class FullscreenLoaderService {
  private showLoaderSubject: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  public showLoader: Observable<boolean> = this.showLoaderSubject.asObservable();

  constructor(private utilsService: UtilService) {}

  public show() {
    if (this.utilsService.isBrowser()) {
      this.showLoaderSubject.next(true);
    }
  }

  public hide() {
    if (this.utilsService.isBrowser()) {
      this.showLoaderSubject.next(false);
    }
  }
}
