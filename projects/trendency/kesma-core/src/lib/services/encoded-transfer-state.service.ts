import { APP_ID, inject, Injectable, TransferState } from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { universalAtob, universalBtoa } from '../utils';
import { UtilService } from './util.service';

function retrieveTransferredState(doc: Document, appId: string): Record<string, unknown | undefined> {
  const script = doc.getElementById(appId + '-state');
  if (script?.textContent) {
    try {
      return JSON.parse(universalAtob(script.textContent)) as NonNullable<unknown>;
    } catch (e) {
      console.warn('Exception while restoring TransferState for app ' + appId, e);
    }
  }

  return {};
}

@Injectable({
  providedIn: 'root',
})
/**
 * This is a TransferState implementation that encodes the state before storing it in the DOM.
 * This is needed because the state is stored in the DOM and therefore needs to be encoded.
 */
export class EncodedTransferState extends TransferState {
  private readonly utils = inject(UtilService);

  constructor() {
    super();
    if (this.utils.isBrowser()) {
      const DOC = inject(DOCUMENT, { optional: true }) || document;
      if (!DOC) {
        throw new Error('DOCUMENT not found');
      }
      this.store = retrieveTransferredState(DOC, inject(APP_ID));
    }
  }

  store: Record<string, unknown | undefined> = {};

  override toJson(): string {
    return universalBtoa(super.toJson());
  }
}
