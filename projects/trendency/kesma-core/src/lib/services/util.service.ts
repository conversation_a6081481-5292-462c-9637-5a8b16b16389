import { DOCUMENT, isPlatformBrowser } from '@angular/common';
import {
  ApplicationRef,
  ComponentFactoryResolver,
  ComponentRef,
  EmbeddedViewRef,
  inject,
  Inject,
  Injectable,
  InjectionToken,
  Injector,
  PLATFORM_ID,
  SecurityContext,
  Type,
} from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { BrowserType, LogType } from '../definitions';

declare let window: Window | any;
declare let InstallTrigger: any;
declare let safari: any;

// TODO Lecserélni ezt egy megbízható külső browser detectorra
// https://github.com/sibiraj-s/browser-dtector#api ; https://github.com/sibiraj-s/ng-browser-detector
// https://www.skypack.dev/view/device-detector-js

@Injectable({
  providedIn: 'root',
})
export class UtilService {
  private readonly document = inject(DOCUMENT);

  constructor(
    @Inject(PLATFORM_ID) private readonly platformId: InjectionToken<Object>,
    private readonly sanitizer: DomSanitizer,
    private readonly componentFactoryResolver: ComponentFactoryResolver,
    private readonly injector: Injector
  ) {}

  /**
   * Ha nincs megadva type, visszaadja hogy böngésző oldalon vagyunk-e.
   * Ha van megadva type, akkor pedig hogy az adott böngésző típuson vagyunk-e
   * @param type
   */
  public isBrowser(type?: BrowserType): boolean {
    const isBrowser = isPlatformBrowser(this.platformId);
    if (type && isBrowser) {
      switch (type) {
        // Opera 8.0+
        case 'opera':
          return (!!window.opr && !!window.opr.addons) || !!window.opera || navigator.userAgent.indexOf(' OPR/') >= 0;
        // Firefox 1.0+
        case 'firefox':
          return typeof InstallTrigger !== 'undefined';
        // Safari 3.0+
        case 'safari':
          return (
            /constructor/i.test(window.HTMLElement) ||
            (function (p) {
              return p.toString() === '[object SafariRemoteNotification]';
            })(!window['safari'] || (typeof safari !== 'undefined' && safari.pushNotification))
          );
        // Edge 20+
        case 'edge':
          return !(this.document as any).documentMode && !!window.StyleMedia;
        // Chrome 1 - 79
        case 'chrome':
          return !!window.chrome && (!!window.chrome.webstore || !!window.chrome.runtime);
        // Edge (based on chromium) detection
        case 'edge-chromium':
          return !!window.chrome && (!!window.chrome.webstore || !!window.chrome.runtime) && navigator.userAgent.indexOf('Edg') !== -1;
      }
    }
    return isBrowser;
  }

  public log(message: string | any, type: LogType = 'info') {
    if (this.isBrowser()) {
      switch (type) {
        case 'info':
          console.log(`%c ${message}`, 'color: blue; background: #cce5ff; display: block; padding: 2px');
          break;
        case 'success':
          console.log(`%c ${message}`, 'color: green; background: #d4edda; display: block; padding: 2px');
          break;
        case 'warning':
          console.warn(message);
          break;
        case 'error':
          console.error(message);
          break;
        default:
          console.log(message);
          break;
      }
    } else {
      console.log(message);
    }
  }

  /**
   * Átalakítja a YouTube videó linket beágyazható formátumra
   * @param link
   */
  public convertYouTubeLinkToEmbed(link: string): string {
    if (link) {
      const embededLink = this.getEmbededYouTubeLink(link);
      const resourceUrl = this.sanitizer.bypassSecurityTrustResourceUrl(embededLink);
      return this.sanitizer.sanitize(SecurityContext.RESOURCE_URL, resourceUrl) || '';
    }
    return '';
  }

  /**
   * Futás közben példányosít egy komponenst és hozzáadja az aktuális oldalhoz a megadott
   * szülő html element alá.
   *
   * @param component
   * @param appRef
   * @param parentElement
   */
  public createDynamicComponent(component: Type<any>, appRef?: ApplicationRef, parentElement?: Element): ComponentRef<any> {
    // 1. Create a component reference from the component
    const componentRef: ComponentRef<any> = this.componentFactoryResolver.resolveComponentFactory(component).create(this.injector);

    // 2. Attach component to the appRef so that it's inside the ng component tree
    if (appRef) {
      appRef.attachView(componentRef.hostView);
    }

    // 3. Get DOM element from component
    const domElement = (componentRef.hostView as EmbeddedViewRef<any>).rootNodes[0] as HTMLElement;

    // 4. Append DOM element to the parent element
    if (parentElement) {
      parentElement.appendChild(domElement);
    }

    return componentRef;
  }

  /**
   * Átalakítja a YouTube videó linket beágyazható formátumra
   * @param link - A YouTube videó link
   * @returns A beágyazható YouTube link
   */
  private getEmbededYouTubeLink = (url: string): string => {
    if (!url) {
      return '';
    }
    if (url.includes('/shorts/')) {
      return url.replace('/shorts/', '/embed/');
    }

    // watch link: https://www.youtube.com/watch?v=abc123
    if (url.includes('watch?v=')) {
      const videoId = new URL(url).searchParams.get('v');
      return `https://www.youtube.com/embed/${videoId}`;
    }
    return url;
  };
}
