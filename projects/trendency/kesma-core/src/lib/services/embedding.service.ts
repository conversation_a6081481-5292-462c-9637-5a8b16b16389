import { ChangeDetectionStrategy, ChangeDetectorRef, Inject, Injectable } from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { DeferIFrameParams } from '../definitions/embedding-service.definitions';
import { UtilService } from './util.service';

declare function embedly(name: string, anchor: HTMLElement, ...args: any): void;

@Injectable({
  providedIn: 'root',
})
export class EmbeddingService {
  constructor(
    @Inject(DOCUMENT) private readonly document: Document,
    private readonly utilsService: UtilService
  ) {}

  // oembedElements type is HTMLCollection
  public createAnchorForEmbedly(oembedElements: any[]): void {
    for (const oembedElement of oembedElements) {
      const hasEmbedly = this.utilsService.isBrowser() ? !!(window as any)?.embedly : false;
      const anchor = this.document.createElement('a');
      anchor.setAttribute('href', oembedElement.getAttribute('url'));

      if (!hasEmbedly) {
        anchor.className = 'embedly-card';
      }
      oembedElement.appendChild(anchor);

      if (hasEmbedly) {
        embedly('card', anchor);
      }
    }
  }

  public loadEmbedMedia(embedMediaElements?: HTMLElement[]) {
    if (this.utilsService.isBrowser()) {
      setTimeout(() => {
        this.reloadTwitter();
        this.reloadInstagram();
        this.reloadTikTok();
        this.reloadFacebook();
        this.reloadFlourish();
        this.reloadWooBox();
        embedMediaElements?.forEach(this.restoreDeferredIframes);
      }, 0);
    }
  }

  /**
   * Defer iframes using native browser lazy loading
   * @param htmlContent
   * @param params
   */
  public deferIframeLoad = (htmlContent: string, params: DeferIFrameParams = { useNativeLazyLoading: false }): string => {
    if (this.utilsService.isBrowser()) {
      const replaceValue = params?.useNativeLazyLoading ? '$1 src="$2" loading="lazy"' : '$1 src="" data-src="$2"';
      //In the browser apply loading lazy and forward.
      return htmlContent.replace(/(<iframe[^>]*?) src="([^"]+)"/gi, replaceValue);
    }
    //In SSR we should not allow the iframe to load, so we change the src to data-src.
    return htmlContent.replace(/(<iframe[^>]*?) src="([^"]+)"/gi, '$1 src="" data-src="$2"');
  };

  private reloadTwitter() {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    const twitter = (window as any)?.twttr;
    if (twitter && twitter.widgets) {
      twitter.widgets.load();
    }
  }

  private reloadInstagram() {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    const instagram = (window as any)?.instgrm;
    if (instagram) {
      instagram.Embeds.process();
    }
  }

  private reloadTikTok() {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    const src = 'https://www.tiktok.com/embed.js';
    const tikTokScripts = this.document.querySelectorAll(`script[src="${src}"]`);
    if (tikTokScripts && tikTokScripts.length) {
      tikTokScripts.forEach((tikTokScript) => tikTokScript?.parentNode?.removeChild(tikTokScript));
      const head = this.document.getElementsByTagName('head')[0];
      const script = this.document.createElement('script');
      script.src = src;
      head.appendChild(script);
    }
  }

  private reloadFacebook() {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    const facebook = (window as any)?.FB;
    if (facebook) {
      facebook.init({ status: true, xfbml: true, version: 'v11.0' });
    }
  }

  private reloadFlourish() {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    (window as any).FlourishLoaded = false;

    const src = 'https://public.flourish.studio/resources/embed.js';
    const head = document.getElementsByTagName('head')[0];
    const scripts = head.querySelectorAll(`script[src="${src}"]`);
    if (!scripts?.length) {
      this.reappendScripts(src);
    }
  }

  private reloadWooBox() {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    (window as any).Woo = undefined;
    const src = 'https://woobox.com/js/plugins/woo.js';
    this.reappendScripts(src, 'woobox-sdk');
  }

  private reappendScripts(scriptSrc: string, scriptId?: string) {
    const scripts = this.document.querySelectorAll(`script[src="${scriptSrc}"]`);
    if (scripts?.length) {
      scripts.forEach((s) => s.parentNode?.removeChild(s));
    }

    const head = this.document.getElementsByTagName('head')[0];
    const script = this.document.createElement('script');
    script.src = scriptSrc;
    if (scriptId) {
      script.id = scriptId;
    }
    head.appendChild(script);
  }

  private readonly restoreDeferredIframes = (iframeContainer: HTMLElement): void => {
    iframeContainer.querySelectorAll<HTMLIFrameElement>('iframe[data-src]').forEach((iframe) => {
      if (!iframe?.dataset?.['src']) {
        return;
      }
      iframe.src = iframe.dataset?.['src'];
    });
  };
}
