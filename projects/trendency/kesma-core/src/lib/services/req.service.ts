import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { catchError, Observable, of, tap, throwError, timeout } from 'rxjs';
import { EnvironmentApiUrl, IHttpOptions } from '../definitions';
import { injectEnvironment } from '../tokens';
import { UtilService } from './util.service';
import { DOCUMENT } from '@angular/common';

@Injectable({
  providedIn: 'root',
})
export class ReqService {
  private readonly timeout = 30 * 1000; // mennyi idő után timeout-oljon, ha a lekérésre nem jön v<PERSON>z (ms)
  protected apiUrl: string;
  private debugEnabled = false;

  /** Minden hívásba beillesztendő header-ek */
  defaultHeaders?: HttpHeaders | Readonly<Record<string, string>> | ReadonlyArray<string>;

  private readonly environment = injectEnvironment();
  private readonly http = inject(HttpClient);
  protected readonly utilsService = inject(UtilService);

  private readonly document = inject(DOCUMENT);

  constructor() {
    this.apiUrl = this.getApiUrl();
    this.timeout = (this.environment.httpReqTimeout || 30_000) * 1000;
    this.debugEnabled = false;
  }

  enableDebug(): void {
    this.debugEnabled = true;
  }

  logHttpError(err: HttpErrorResponse, url: string, type = 'GET'): void {
    if (!this.debugEnabled) {
      if (err.status !== 404) {
        console.error(`[${err.name}] ${type}: ${url}: #${err.status ?? err.message} - ${err.statusText}`);
      }
      return;
    }
    console.error(`${type}: ${url}: `, err);
  }

  /**
   * GET hívás indítása automatikus SSR state transfer kezeléssel
   *
   * @param subUrl hívandó relatív API url
   * @param options opcionális hívás configuráció
   *
   * @returns Observable az API hívás eredményével
   */
  get<T>(subUrl: string, options: IHttpOptions | any = {}): Observable<T> {
    const url = this.resolveUrl(subUrl);
    options.headers = {
      ...(this.defaultHeaders ?? {}),
      ...(options.headers ?? {}),
    };

    return this.http.get<T>(url, options).pipe(
          // @ts-ignore
          timeout(this.timeout),
          catchError((err: HttpErrorResponse, caught: Observable<T>) => {
            this.logHttpError(err, url, 'GET');
            return throwError(() => err);
          }),
        );
  }

  /**
   * POST hívás indítása automatikus SSR state transfer kezeléssel
   *
   * @param subUrl  hívandó relatív API url
   * @param data    body-ban küldendő tartalom
   * @param options opcionális hívás configuráció
   *
   * @returns Observable az API hívás eredményével
   */
  post<T>(subUrl: string, data: any, options: IHttpOptions | any = {}): Observable<T> {
    const url = this.resolveUrl(subUrl);
    options.headers = {
      ...(this.defaultHeaders ?? {}),
      ...(options.headers ?? {}),
    };
    this.utilsService.log(`POST: ${url}`);

    return this.http.post<T>(url, data, options).pipe(
          // @ts-ignore
          timeout(this.timeout),
          catchError((err: HttpErrorResponse, caught: Observable<T>) => {
            this.logHttpError(err, url, 'POST');
            return throwError(() => err);
          })
        );
  }

  /**
   * PUT hívás indítása automatikus SSR state transfer kezeléssel
   *
   * @param subUrl  hívandó relatív API url
   * @param data    body-ban küldendő tartalom
   * @param options opcionális hívás configuráció
   *
   * @returns Observable az API hívás eredményével
   */
  put<T>(subUrl: string, data: any, options: IHttpOptions | any = {}): Observable<T> {
    const url = this.resolveUrl(subUrl);
    options.headers = {
      ...(this.defaultHeaders ?? {}),
      ...(options.headers ?? {}),
    };
    this.utilsService.log(`PUT: ${url}`);

    return this.http.put<T>(url, data, options).pipe(
          // @ts-ignore
          timeout(this.timeout),
          catchError((err: HttpErrorResponse, caught: Observable<T>) => {
            this.logHttpError(err, url, 'PUT');
            return throwError(() => err);
          })
        );
  }

  /**
   * PATCH hívás indítása automatikus SSR state transfer kezeléssel
   *
   * @param subUrl  hívandó relatív API url
   * @param data    body-ban küldendő tartalom
   * @param options opcionális hívás configuráció
   *
   * @returns Observable az API hívás eredményével
   */
  patch<T>(subUrl: string, data: any, options: IHttpOptions | any = {}): Observable<T> {
    const url = this.resolveUrl(subUrl);
    options.headers = {
      ...(this.defaultHeaders ?? {}),
      ...(options.headers ?? {}),
    };

    this.utilsService.log(`PATCH: ${url}`);

    return this.http.patch<T>(url, data, options).pipe(
          // @ts-ignore
          timeout(this.timeout),
          catchError((err: HttpErrorResponse, caught: Observable<T>) => {
            this.logHttpError(err, url, 'PATCH');
            return throwError(() => err);
          })
        );
  }

  /**
   * DELETE hívás indítása automatikus SSR state transfer kezeléssel
   *
   * @param subUrl hívandó relatív API url
   * @param options opcionális hívás configuráció
   *
   * @returns Observable az API hívás eredményével
   */
  delete<T>(subUrl: string, options: IHttpOptions | any = {}): Observable<T> {
    const url = this.resolveUrl(subUrl);
    options.headers = {
      ...(this.defaultHeaders ?? {}),
      ...(options.headers ?? {}),
    };

    this.utilsService.log(`DELETE: ${url}`);

    return this.http.delete<T>(url, options).pipe(
          // @ts-ignore
          timeout(this.timeout),
          catchError((err: HttpErrorResponse, caught: Observable<T>) => {
            this.logHttpError(err, url, 'DELETE');
            return throwError(() => err);
          }),

        );
  }

  /**
   * Feloldja az API urljét az environment configból:
   * * ha az apiUrl string-ként van megadva, akkor használja azt
   * * ha pedig külön van választva, akkor böngésző oldalon a clientApiUrl-t,
   *   SSR oldalon pedig a serverApiUrl-t
   *
   * @private
   */
  private getApiUrl(): string {
    if (!this.environment.apiUrl) {
      return '';
    }

    if (typeof this.environment.apiUrl === 'string') {
      return this.environment.apiUrl;
    }

    const { clientApiUrl, serverApiUrl } = this.environment.apiUrl as EnvironmentApiUrl;
    return this.utilsService.isBrowser() ? clientApiUrl : serverApiUrl;
  }

  /**
   * Előkészíti az ismeretlen URL-t, hogy mindenképp abszolút legyen és ha böngésző oldali
   * a kérés akkor a protokoll is egyezzen az éppen aktuálisan használttal (http/https).
   * Ez megakadályozza a böngészőben a "mixed mode" -ból fakadó blokkolásokat.
   *
   * @param url
   * @private
   */
  private resolveUrl(url: string): string {
    const absoluteUrlPattern = /^https?:\/\//i;

    // Ha abszolút url-t hív, nem kell semmit módosítani
    if (absoluteUrlPattern.test(url)) {
      return url;
    }

    // Ha böngésző oldali a hívás és abszolút apiUrl van megadva, meg kell vizsgálni hogy az apiUrl protokollja
    // megegyezik-e a weboldal protokolljával és ha nem, akkor ehhez igazítani az apiUrl-t
    let apiUrl = this.apiUrl;
    if (this.utilsService.isBrowser() && absoluteUrlPattern.test(this.apiUrl)) {
      apiUrl = this.matchClientApiUrlWithWebsiteProtocol(absoluteUrlPattern);
    }

    if (url.indexOf('/') === 0) {
      const subUrl = url.slice(1, url.length);
      return `${apiUrl}/${subUrl}`;
    }
    return `${apiUrl}/${url}`;
  }



  /**
   * Ha a böngésző oldali API url protokollja nem egyezik meg a weboldal protokolljával,
   * akkor használja a weboldal protokollját
   *
   * @param absoluteUrlPattern
   * @private
   */
  private matchClientApiUrlWithWebsiteProtocol(absoluteUrlPattern: RegExp): string {
    const websiteProtocol = `${this.document.location.protocol}//`;
    const clientApiUrlProtocol = this.apiUrl.match(absoluteUrlPattern)?.[0] ?? '';

    return websiteProtocol !== clientApiUrlProtocol && this.environment.type !== 'local'
      ? this.apiUrl.replace(clientApiUrlProtocol, websiteProtocol)
      : this.apiUrl;
  }


}
