import { inject, Injectable } from '@angular/core';
import { catchError, delay, filter, map, mergeMap, timeout } from 'rxjs/operators';
import { BehaviorSubject, finalize, Observable, of, take } from 'rxjs';
import { ActivatedRouteSnapshot, NavigationEnd, Router } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DOCUMENT } from '@angular/common';
import { SeoDomService } from './seo-dom.service';
import { injectEnvironment, REQUEST } from '../tokens';
import { ReqService } from './req.service';
import { UtilService } from './util.service';
import { IMetaData, SeoCanonicalDataParams, SeoMeta, SeoMetaDataParams } from '../definitions';

export const DefaultCanonicalDataParams: SeoCanonicalDataParams = { skipSeoMetaCheck: false, addHostUrl: true };
export const DefaultSeoMetaDataParams: SeoMetaDataParams = { skipSeoMetaCheck: false, canRobotsBeEmpty: false };

@Injectable({
  providedIn: 'root',
})
export class SeoService {
  readonly seoDom = inject(SeoDomService);
  readonly environment = injectEnvironment();
  readonly reqService = inject(ReqService);
  readonly router = inject(Router);
  readonly utilsService = inject(UtilService);
  readonly document = inject(DOCUMENT);
  readonly request = inject(REQUEST, { optional: true });
  readonly #seoMetaToolData = new BehaviorSubject<Readonly<Record<string, SeoMeta>>>({});
  private readonly urlsInOProgress = new BehaviorSubject<string[]>([]);

  /**
   * Keeps track of the meta data that has been set for a specific url.
   * This is needed because the meta data is also set then the urls is changed and the existing
   * set data removed.
   * @private
   */
  private readonly isMetaSetForUrl: Record<string, boolean> = {};

  constructor() {
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        delay(500),
        takeUntilDestroyed()
      )
      .subscribe(() => {
        const shouldSkip = this.getRouteDataProperty<boolean>('skipSeoMetaCheck');
        if (shouldSkip) {
          return;
        }
        const url = this.getCurrentRelativeUrl();
        if (url.length <= 1) {
          return;
        }
        this.setMetaToolSeo(url);
      });
  }

  /**
   * Returns the host url.
   */
  get hostUrl(): string {
    return this.seoDom.hostUrl;
  }

  /**
   * Returns the current url.
   */
  get currentUrl(): string {
    return this.seoDom.currentUrl;
  }

  /**
   * Sets the metadata for the provided url.
   * It will first check if the data is already in the cache. If not, it will request the data from the API.
   * This is called when a new url is loaded in the router.
   * @param url
   */
  setMetaToolSeo(url: string): void {
    this.getSeoMetaForUrl(url).subscribe((seoMetaToolData) => {
      if (Object.keys(seoMetaToolData).length > 0 && !this.isMetaSetForUrl[url]) {
        // We can supply empty parameters as they will be automatically applied from the cache.
        // It should not produce any problems as if we have any set meta data, it should fully override any predefined metadata.
        this.setMetaData({});
        this.updateCanonicalUrl('');
      }
    });
  }

  /**
   * Handles the provided url and adds the SEO Meta Tool data to the cache.
   * @param url
   */
  handleUrl(url: string): void {
    this.#requestSeoMetaForUrl(url)
      .pipe(take(1))
      .subscribe((seoMetaToolData) => {
        this.addSeoMetaToolData(url, seoMetaToolData);
      });
  }

  /**
   * Adds the SEO Meta Tool data to the cache.
   * @param url
   * @param seoMetaToolData
   */
  addSeoMetaToolData(url: string, seoMetaToolData: SeoMeta): void {
    const added = { ...this.#seoMetaToolData.getValue(), [url]: seoMetaToolData };
    this.#seoMetaToolData.next(added);
  }

  /**
   * Returns the current relative url.
   */
  getCurrentRelativeUrl(): string {
    return this.router.url;
  }

  /**
   * Returns the SEO Meta Tool data for the provided url.
   * It will first check if the data is already in the cache. If not, it will request the data from the API.
   * @param relativeUrl
   */
  getSeoMetaForUrl(relativeUrl: string): Observable<SeoMeta> {
    const urls = Object.keys(this.#seoMetaToolData.getValue());
    return this.urlsInOProgress.asObservable().pipe(
      mergeMap((urlsInOProgress) => {
        if (!urls.includes(relativeUrl) && !urlsInOProgress.includes(relativeUrl)) {
          this.handleUrl(relativeUrl);
        }
        return this.#seoMetaToolData.asObservable().pipe(
          filter((seoMetaToolData) => relativeUrl in seoMetaToolData),
          timeout(5_000),
          map((seoMetaToolData) => {
            return seoMetaToolData[relativeUrl];
          }),
          catchError(() => {
            return of({});
          })
        );
      }),
      take(1)
    );
  }

  /**
   * Return the value of the provided property from the current route data.
   * This function is explicitly needed as this service is injected in root level. It could not access the
   * activated route data, because it would always return the app component data.
   * Hence, we need to manually traverse the route tree to find the data.
   * @param propertyName
   * @private
   */
  private getRouteDataProperty<T>(propertyName: string): T | undefined {
    // Start with the current router state
    let routeSnapshot: ActivatedRouteSnapshot | null = this.router.routerState.snapshot.root;

    // Traverse the route tree
    while (routeSnapshot) {
      if (routeSnapshot.data && propertyName in routeSnapshot.data) {
        return routeSnapshot.data[propertyName];
      }
      routeSnapshot = routeSnapshot.firstChild;
    }

    // If the property wasn't found
    return;
  }

  /**
   * Returns the request for the provided url for the SEO Meta API.
   * @param relativeUrl
   * @private
   */
  #requestSeoMetaForUrl(relativeUrl: string): Observable<SeoMeta> {
    this.urlsInOProgress.next([...this.urlsInOProgress.value, relativeUrl]);
    return this.reqService
      .get<{
        data: SeoMeta;
      }>('seo-meta-tool/seo-meta-tool-information', { params: { relativeUrl } })
      .pipe(
        map(({ data }) => data),
        timeout(5_000), // To avoid slow responses in SSR.
        catchError(() => {
          return of({}); // If there is an error, threat it like there is no data.
        }),
        finalize(() => {
          this.urlsInOProgress.next(this.urlsInOProgress.value.filter((url) => url !== relativeUrl));
        })
      );
  }

  setMetaData(metaData: IMetaData = this.seoDom.defaultMeta, params?: SeoMetaDataParams): void {
    params = { ...DefaultSeoMetaDataParams, ...params };
    if (params?.skipSeoMetaCheck) {
      this.seoDom.setMetaData(metaData, params);
      return;
    }
    this.getSeoMetaForUrl(this.getCurrentRelativeUrl()).subscribe((seoMetaToolData) => {
      if (Object.keys(seoMetaToolData).length > 0) {
        const meta: IMetaData = {
          ...metaData,
          title: seoMetaToolData.title,
          ogTitle: seoMetaToolData.title,
          description: seoMetaToolData.description,
          ogDescription: seoMetaToolData.description,
          robots: seoMetaToolData.robots?.join(','),
        };
        this.seoDom.setMetaData(meta, params);
        this.isMetaSetForUrl[this.getCurrentRelativeUrl()] = true;
        return;
      }
      this.isMetaSetForUrl[this.getCurrentRelativeUrl()] = true;
      this.seoDom.setMetaData(metaData, params);
    });
  }

  updateCanonicalUrl(url: string, params?: SeoCanonicalDataParams): void {
    params = { ...DefaultCanonicalDataParams, ...params };
    if (params?.skipSeoMetaCheck) {
      this.seoDom.updateCanonicalUrl(url, params.addHostUrl);
      return;
    }
    this.getSeoMetaForUrl(this.getCurrentRelativeUrl()).subscribe((seoMetaToolData) => {
      if (seoMetaToolData.canonical) {
        this.seoDom.updateCanonicalUrl(seoMetaToolData.canonical, false);
        return;
      }
      this.seoDom.updateCanonicalUrl(url, params && 'addHostUrl' in params ? params?.addHostUrl : true);
    });
  }
}
