import { Inject, Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { SeoDomService } from './seo-dom.service';
import { ArticleSchema, PublishableSchema, SchemaOrg, SchemaOrgUtils, SchemaOrgWebpageDataTemplate } from '../utils';
import { Environment } from '../definitions';
import { AppEnvironment } from '../tokens';

@Injectable({
  providedIn: 'root',
})
export class SchemaOrgService {
  private renderer: Renderer2;
  private readonly schemaScriptTag: HTMLScriptElement;

  constructor(
    @Inject(AppEnvironment) private readonly environment: Environment,
    private readonly seo: SeoDomService,
    private rendererFactory: RendererFactory2
  ) {
    this.renderer = this.rendererFactory.createRenderer(null, null);
    this.schemaScriptTag = this.renderer.selectRootElement(SchemaOrgUtils.className, true);
  }

  /**
   * Stores the inserted schemas in order to manage them in one place.
   */
  private _schemas: SchemaOrg[] = [];

  public get schemas(): SchemaOrg[] {
    return this._schemas;
  }

  /**
   * Removes all structured schema data from the DOM and from the service.
   */
  removeStructuredData(): void {
    this._schemas = [];
    this.renderer.setProperty(this.schemaScriptTag, 'innerHTML', '');
  }

  /**
   * Adds a new schema to the existing structured schema.
   * It will append after the already existing schemas.
   * @param schemaArg Schema to insert.
   */
  insertSchema(schemaArg?: SchemaOrg): void {
    if ((schemaArg as PublishableSchema).datePublished) {
      schemaArg = this.correctPublishDates(schemaArg as PublishableSchema);
    }
    const schema: SchemaOrg =
      schemaArg ??
      ({
        ...SchemaOrgWebpageDataTemplate,
        publisher: {
          ...SchemaOrgWebpageDataTemplate.publisher,
          name: this.environment.siteName ?? '',
        },
        url: this.seo.hostUrl,
      } as ArticleSchema);

    this._schemas.push(schema);
    this.renderSchemas();
  }

  private correctPublishDates(schema: PublishableSchema): PublishableSchema {
    let correctedSchema = {
      ...schema,
      datePublished: schema.datePublishedString ?? schema.datePublished,
      dateModified: schema.dateModifiedString ?? schema.dateModified,
    };
    if (correctedSchema.datePublishedString) {
      delete correctedSchema.datePublishedString;
    }
    if (correctedSchema.dateModifiedString) {
      delete correctedSchema.dateModifiedString;
    }
    return correctedSchema;
  }

  /**
   * Removes the given schema instance.
   * @param schemaArg Schema instance to remove.
   */
  removeSchema(schemaArg: SchemaOrg): void {
    this._schemas = this._schemas.filter((schema) => schema !== schemaArg);
    this.renderSchemas();
  }

  /**
   * Renders the internal array of schemas to the browser's DOM.
   */
  private renderSchemas(): void {
    this.schemaScriptTag.innerText = '';
    const scriptText = this.renderer.createText(
      JSON.stringify({
        '@context': 'http://schema.org',
        '@graph': this.schemas,
      })
    );

    //Remove old content of the schema script tag.
    this.schemaScriptTag.innerHTML = '';

    this.renderer.appendChild(this.schemaScriptTag, scriptText);
  }
}
