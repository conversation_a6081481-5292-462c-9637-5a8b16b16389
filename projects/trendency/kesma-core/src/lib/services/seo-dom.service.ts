import { Inject, Injectable, Optional } from '@angular/core';
import { Meta, MetaDefinition, Title } from '@angular/platform-browser';
import { Environment, IMetaData, SeoMetaDataParams } from '../definitions';
import { UtilService } from './util.service';
import type { Request } from 'express';
import { DOCUMENT } from '@angular/common';
import { AppEnvironment, REQUEST } from '../tokens';
import { format } from 'date-fns';

const defaultMetaInfo: IMetaData = {
  title: '',
  description: '',
  keywords: '',
  robots: '',
  ogTitle: '',
  ogUrl: '',
  ogImage: '',
  ogType: '',
  ogDescription: '',
  ogLocale: '',
  ogSiteName: '',
  articlePublishedTime: '',
  articleModifiedTime: '',
  articleAuthor: '',
  author: '',
  twitterSiteName: '',
  twitterTitle: '',
  twitterDescription: '',
  twitterImage: '',
};

@Injectable({
  providedIn: 'root',
})
export class SeoDomService {
  readonly defaultMeta = defaultMetaInfo;

  constructor(
    @Inject(AppEnvironment) private readonly environment: Environment,
    private readonly meta: Meta,
    private readonly titleService: Title,
    private readonly utilsService: UtilService,
    @Optional() @Inject(REQUEST) private readonly request: Request,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  get hostUrl() {
    return this.utilsService.isBrowser()
      ? this.document.location.origin
      : (this.environment.siteUrl ?? `${this.request.protocol}://${this.request.get('host')}`);
  }

  get currentUrl() {
    return this.utilsService.isBrowser() ? this.document.location.href : `${this.hostUrl}${this.request.url}`;
  }

  public setMetaData(metaData: IMetaData = this.defaultMeta, params: SeoMetaDataParams = {}) {
    if (metaData.title) {
      this.setTitle(metaData.title);
    }
    this.updateTag({
      name: 'robots',
      content: metaData.robots || (params.canRobotsBeEmpty ? '' : 'index, follow'),
    });
    this.updateTag({
      name: 'description',
      content: metaData.description || '',
    });
    this.updateTag({
      name: 'keywords',
      content: metaData.keywords || '',
    });
    this.updateTag({
      name: 'og:title',
      property: 'og:title',
      content: metaData.ogTitle || '',
    });
    this.updateTag({
      name: 'og:url',
      property: 'og:url',
      content: metaData.ogUrl || this.currentUrl || '',
    });
    this.updateTag({
      name: 'og:image',
      property: 'og:image',
      content: metaData.ogImage || `${this.hostUrl}/assets/images/og_image.png` || '',
    });
    this.updateTag({
      name: 'og:image:width',
      property: 'og:image:width',
      content: metaData.ogImageWidth || '1200',
    });
    this.updateTag({
      name: 'og:image:height',
      property: 'og:image:height',
      content: metaData.ogImageHeight || '630',
    });
    this.updateTag({
      name: 'og:image:secure_url',
      property: 'og:image:secure_url',
      content: metaData.ogImage || `${this.hostUrl}/assets/images/og_image.png` || '',
    });
    this.updateTag({
      name: 'og:type',
      property: 'og:type',
      content: metaData.ogType || 'website',
    });
    this.updateTag({
      name: 'og:description',
      property: 'og:description',
      content: metaData.ogDescription || metaData.description || '',
    });
    this.updateTag({
      name: 'og:locale',
      content: metaData.ogLocale || 'hu_HU',
    });
    this.updateTag({
      name: 'og:site_name',
      property: 'og:site_name',
      content: metaData.ogSiteName || metaData.ogTitle || metaData.title || '',
    });
    this.updateTag({
      name: 'article:published_time',
      property: 'article:published_time',
      content: metaData.articlePublishedTime ? format(new Date(metaData.articlePublishedTime), "yyyy-MM-dd'T'HH:mm:ssXXX") : '',
    });
    this.updateTag({
      name: 'article:modified_time',
      property: 'article:modified_time',
      content: metaData.articleModifiedTime ? format(new Date(metaData.articleModifiedTime), "yyyy-MM-dd'T'HH:mm:ssXXX") : '',
    });
    this.updateTag({
      name: 'article:author',
      property: 'article:author',
      content: metaData.articleAuthor || '',
    });
    this.updateTag({
      name: 'author',
      property: 'author',
      content: metaData.author || '',
    });
    this.updateTag({
      name: 'twitter:card',
      content: 'summary_large_image',
    });
    this.updateTag({
      name: 'twitter:site',
      content: metaData.twitterSiteName || metaData.ogSiteName || '',
    });
    this.updateTag({
      name: 'twitter:creator',
      content: metaData.articleAuthor || '',
    });
    this.updateTag({
      name: 'twitter:title',
      content: metaData.twitterTitle || metaData.ogTitle || metaData.title || '',
    });
    this.updateTag({
      name: 'twitter:description',
      content: metaData.twitterDescription || metaData.ogDescription || metaData.description || '',
    });
    this.updateTag({
      name: 'twitter:image',
      content: metaData.twitterImage || metaData.ogImage || `${this.hostUrl}/assets/images/og_image.png`,
    });

    if (metaData.canonicalUrl) {
      this.updateCanonicalUrl(metaData.canonicalUrl);
    }
  }

  private updateTag(tag: MetaDefinition) {
    this.meta.updateTag(tag);
  }

  public setTitle(title: string) {
    this.titleService.setTitle(title);
  }

  public getTitle(): string {
    return this.titleService.getTitle();
  }

  public setDefaultMetaData() {
    this.setMetaData(defaultMetaInfo);
  }

  /**
   * Updates the canonical link tag in the DOM.
   * @param url The URL to be used as the Canonical URL
   * @param addHostUrl Prefix the URL with the host URL (for relative URLs)
   */
  updateCanonicalUrl(url: string, addHostUrl = true): void {
    const head = this.document.getElementsByTagName('head')[0];
    let element: HTMLLinkElement | null = this.document.querySelector('link[rel="canonical"]');
    if (!element) {
      element = this.document.createElement('link') as HTMLLinkElement;
      head.appendChild(element);
    }
    element.setAttribute('rel', 'canonical');
    element.setAttribute('href', (addHostUrl ? this.hostUrl + '/' : '') + url);
  }
}
