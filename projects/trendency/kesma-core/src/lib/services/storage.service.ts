import { Inject, Injectable, Optional } from '@angular/core';
import { UtilService } from './util.service';
import { DOCUMENT } from '@angular/common';
import { REQUEST } from '../tokens';

@Injectable({
  providedIn: 'root',
})
export class StorageService {
  constructor(
    private readonly utilsService: UtilService,
    @Inject(DOCUMENT) private readonly document: Document,
    @Inject(REQUEST) @Optional() private readonly request: Request
  ) {}

  setCookie<T = any>(key: string, data: T, expirationInSecs?: number, domain?: string): void {
    if (this.utilsService.isBrowser()) {
      const expiration: Date | null = typeof expirationInSecs !== 'undefined' ? new Date(new Date().getTime() + expirationInSecs * 1000) : null;
      const stringifiedData: string = typeof data === 'string' ? data : JSON.stringify(data);
      const cookieData = {
        [key]: stringifiedData,
        path: '/',
        ...(expiration ? { expires: expiration.toUTCString() } : {}),
        ...(domain ? { domain } : {}),
      };
      document.cookie = this.createCookieString(cookieData);
    }
  }

  getCookie(key: string, cookies?: string, defaultValue: string | undefined = undefined): string | undefined {
    if (!this.utilsService.isBrowser()) {
      return undefined;
    }

    const value: string = `; ${cookies || this.document.cookie}`;
    const parts: string[] = value.split(`; ${key}=`);
    return parts.length === 2 ? parts.pop()?.split(';').shift() : defaultValue;
  }

  removeCookie(key: string): void {
    this.setCookie(key, null, -1);
  }

  isSessionStorageAvailable(): boolean {
    return this.isStorageAvailable(StorageType.SESSION_STORAGE);
  }

  setSessionStorageData<T = any>(key: string, data: T): void {
    this.setStorageData<T>(StorageType.SESSION_STORAGE, key, data);
  }

  getSessionStorageData<T = any>(key: string, defaultValue: T | null = null): T | null {
    return this.getStorageData<T>(StorageType.SESSION_STORAGE, key, defaultValue);
  }

  removeSessionStorageData(key: string): void {
    this.removeStorageData(StorageType.SESSION_STORAGE, key);
  }

  isLocalStorageAvailable(): boolean {
    return this.isStorageAvailable(StorageType.LOCAL_STORAGE);
  }

  setLocalStorageData<T = any>(key: string, data: T): void {
    this.setStorageData<T>(StorageType.LOCAL_STORAGE, key, data);
  }

  getLocalStorageData<T = any>(key: string, defaultValue: T | null = null): T | null {
    return this.getStorageData<T>(StorageType.LOCAL_STORAGE, key, defaultValue);
  }

  removeLocalStorageData(key: string): void {
    this.removeStorageData(StorageType.LOCAL_STORAGE, key);
  }

  private getStorageData<T = any>(storageType: StorageType, key: string, defaultValue: T | null = null): T | null {
    if (this.utilsService.isBrowser()) {
      let rawData: string | null;
      try {
        if (storageType === StorageType.LOCAL_STORAGE && this.document.defaultView?.localStorage) {
          rawData = this.document.defaultView?.localStorage?.getItem(key) || null;
        } else {
          rawData = this.document.defaultView?.sessionStorage?.getItem(key) || null;
        }
      } catch (e) {
        rawData = null;
        console.warn('Access to localStorage/sessionStorage is currently disabled.');
      }

      let savedData: T | null;
      try {
        savedData = (rawData ?? '').length > 0 ? JSON.parse(rawData as string) : rawData;
      } catch (e) {
        savedData = rawData as any;
      }
      return savedData || defaultValue;
    } else {
      // SSR
      if (this.request) {
        if (!(this.request as any)['ssrData']) {
          (this.request as any)['ssrData'] = {};
        }
        const ssrData = (this.request as any)['ssrData'];
        return ssrData[key] || defaultValue;
      }
    }

    return null;
  }

  private setStorageData<T = any>(storageType: StorageType, key: string, data: T): void {
    if (this.utilsService.isBrowser()) {
      try {
        if (storageType === StorageType.LOCAL_STORAGE && this.document.defaultView?.localStorage) {
          this.document.defaultView?.localStorage?.setItem(key, JSON.stringify(data));
        } else {
          this.document.defaultView?.sessionStorage?.setItem(key, JSON.stringify(data));
        }
      } catch (e) {
        console.warn('Access to localStorage/sessionStorage is currently disabled.');
      }
    } else {
      // SSR
      if (this.request) {
        if (!(this.request as any)['ssrData']) {
          (this.request as any)['ssrData'] = {};
        }
        (this.request as any)['ssrData'][key] = data;
      }
    }
  }

  private removeStorageData(storageType: StorageType, key: string): void {
    if (this.utilsService.isBrowser()) {
      try {
        if (storageType === StorageType.LOCAL_STORAGE && this.document.defaultView?.localStorage) {
          this.document.defaultView?.localStorage?.removeItem(key);
        } else {
          this.document.defaultView?.sessionStorage?.removeItem(key);
        }
      } catch (e) {
        console.warn('Access to localStorage/sessionStorage is currently disabled.');
      }
    }
  }

  private createCookieString(cookieObject: Record<string, string>): string {
    // Check if the input is an object
    if (typeof cookieObject !== 'object' || cookieObject === null) {
      throw new Error('Cookie string input must be an object');
    }

    // Initialize an empty string to store the cookie string
    let cookieString = '';

    // Iterate through the object and concatenate key-value pairs
    for (const key in cookieObject) {
      if (Object.prototype.hasOwnProperty.call(cookieObject, key)) {
        // Concatenate key-value pair
        cookieString += `${key}=${cookieObject[key]}; `;
      }
    }

    // Remove the trailing "; " from the end
    cookieString = cookieString.slice(0, -2);

    return cookieString;
  }

  private isStorageAvailable(storageType: StorageType): boolean {
    try {
      if (storageType === StorageType.LOCAL_STORAGE) {
        return !!this.document.defaultView?.localStorage;
      }

      return !!this.document.defaultView?.sessionStorage;
    } catch (e) {
      return false;
    }
  }
}

export enum StorageType {
  LOCAL_STORAGE = 'LOCAL_STORAGE',
  SESSION_STORAGE = 'SESSION_STORAGE',
}
