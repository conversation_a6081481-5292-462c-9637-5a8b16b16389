import { Component, OnDestroy, OnInit } from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { FullscreenLoaderService } from '../../services';
import { NgIf } from '@angular/common';

@Component({
  selector: 'tr-fullscreen-loader',
  templateUrl: './fullscreen-loader.component.html',
  styleUrls: ['./fullscreen-loader.component.scss'],
  imports: [NgIf],
})
export class TrFullscreenLoaderComponent implements OnInit, OnDestroy {
  public showLoader?: boolean;
  private unsubscribe = new Subject();

  constructor(private fullscreenLoaderService: FullscreenLoaderService) {}

  ngOnInit() {
    this.fullscreenLoaderService.showLoader.pipe(takeUntil(this.unsubscribe)).subscribe((show) => {
      this.showLoader = show;
    });
  }

  ngOnDestroy() {
    this.unsubscribe.next(true);
    this.unsubscribe.complete();
  }

  public show() {
    this.fullscreenLoaderService.show();
  }

  public hide() {
    this.fullscreenLoaderService.hide();
  }
}
