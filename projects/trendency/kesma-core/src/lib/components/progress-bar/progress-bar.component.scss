:host {
  display: block;
  position: relative;

  .tr-progress-bar-container {
    pointer-events: none;
    transition: 350ms linear all;
    color: var(--tr-progressbar-container-color, #29d);

    .tr-progress-bar {
      transition: width 350ms;

      background: var(--tr-progrssbar-color, #29d);
      position: fixed;
      z-index: 10002;
      top: 0;
      left: 0;
      width: 100%;
      height: 2px;
      border-bottom-right-radius: 1px;
      border-top-right-radius: 1px;
    }

    .peg {
      display: none;
      position: absolute;
      width: 70px;
      right: 0;
      top: 0;
      height: 2px;
      opacity: 0.45;
      box-shadow: 1px 0 6px 1px;
      color: inherit;
      border-radius: 100%;
    }
  }
}
