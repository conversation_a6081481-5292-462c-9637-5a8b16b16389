import { Component, Input, OnInit } from '@angular/core';
import { ProgressBarService } from '../../services';
import { ProgressBarOptions } from '../../definitions';
import { AsyncPipe, NgIf } from '@angular/common';

const DefaultOptions: ProgressBarOptions = {
  value: undefined,
};

@Component({
  selector: 'tr-progressbar',
  templateUrl: 'progress-bar.component.html',
  styleUrls: ['./progress-bar.component.scss'],
  imports: [NgIf, AsyncPipe],
})
export class TrProgressBarComponent implements OnInit {
  @Input() public options?: ProgressBarOptions;

  constructor(public progressBarService: ProgressBarService) {}

  ngOnInit() {
    this.options = { ...DefaultOptions, ...(this.options || {}) };
  }
}
