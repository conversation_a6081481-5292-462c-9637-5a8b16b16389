import { Pipe, PipeTransform } from '@angular/core';
import { differenceInCalendarDays, differenceInMinutes, format, isYesterday } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';
import { hu } from 'date-fns/locale/hu';

const ONE_MINUTE = 1;
const ONE_HOUR = 60;
const ONE_DAY = 1;

/**
 * @deprecated old portals use only
 */
@Pipe({
  name: 'messageDate',
})
export class MessageDatePipe implements PipeTransform {
  transform(value: string | any): string {
    if (!value) {
      return '';
    }

    const now = new Date();

    let publishDate;
    if (value instanceof Date) {
      publishDate = value;
    } else if (value.date) {
      publishDate = new Date(`${value.date.replace(' ', 'T')}Z`);
    } else {
      publishDate = new Date(`${value.replace(' ', 'T')}Z`);
    }
    const timezoned = toZonedTime(publishDate, 'Europe/Budapest');

    const minutes = differenceInMinutes(now, timezoned);
    const days = differenceInCalendarDays(now, timezoned);

    const isZeroMinute = minutes === 0;

    if (minutes < 1) {
      // Kevesebb, mint 1 perce
      return 'Most';
    }

    if (minutes >= 1 && minutes < ONE_HOUR) {
      // 1-59 perce
      return `${isZeroMinute ? ONE_MINUTE : minutes} perce`;
    }
    if (ONE_DAY > days) {
      // 1-23 órája
      return `Ma ${format(timezoned, 'HH:mm', { locale: hu })}`;
    }
    if (isYesterday(timezoned)) {
      // Tegnap
      return `Tegnap, ${format(timezoned, 'HH:mm', { locale: hu })}`;
    } else {
      return format(timezoned, 'yyyy.MM.dd HH:mm', { locale: hu });
    }
  }
}
