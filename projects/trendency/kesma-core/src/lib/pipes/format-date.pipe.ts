import { Pipe, PipeTransform } from '@angular/core';
import { format } from 'date-fns-tz';
import { hu } from 'date-fns/locale';
import { DateFormat } from '../definitions';
import { ConversionOption, convertDateFromLocalToUTC, convertDateFromUTCToLocal, toDate } from '../utils';

// TODO make this multilanguage

@Pipe({
  name: 'formatDate',
})
export class FormatDatePipe implements PipeTransform {
  transform(value: string | Date, formatType: DateFormat, convertTo: ConversionOption = 'local'): string {
    let dateFormat = 'yyyy. LLLL d.';

    if (!value) {
      return '';
    }

    switch (formatType) {
      case 'month':
        dateFormat = 'LL';
        break;
      case 'year':
        dateFormat = 'yyyy';
        break;
      case 'date':
        dateFormat = 'yyyy.MM.dd';
        break;
      case 'h-m-s':
        dateFormat = 'HH:mm:ss';
        break;
      case 'h-m':
        dateFormat = 'HH:mm';
        break;
      case 'l-d':
        dateFormat = 'LLLL d.';
        break;
      case 'l-d-e':
        dateFormat = 'LLLL d. EEEE';
        break;
      case 'l-d-h-m':
        dateFormat = 'LLL d. HH:mm';
        break;
      case 'y-l-d-h-m':
        dateFormat = 'yyyy. LLLL d. HH:mm';
        break;
      case 'dateTime':
        dateFormat = 'yyyy.MM.dd. HH:mm:ss';
        break;
      case 'hungarianLong':
        dateFormat = 'yyyy. LLLL d. EEEE HH:mm';
        break;
      case 'hungarian':
        dateFormat = 'yyyy. LLLL d.';
        break;
      case 'y-l-m-d-h-m':
        dateFormat = 'yyyy.MM.dd. HH:mm';
        break;
    }

    const date: Date = toDate(value, convertTo);

    switch (convertTo) {
      case 'local': {
        const localDate = convertDateFromUTCToLocal(date);
        return format(localDate, dateFormat, {
          locale: hu,
        });
      }
      case 'utc': {
        const utcDate = convertDateFromLocalToUTC(date);
        return format(utcDate, dateFormat, {
          locale: hu,
        });
      }
    }
  }
}
