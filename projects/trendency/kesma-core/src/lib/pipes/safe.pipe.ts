import { <PERSON>pe, PipeTransform, SecurityContext } from '@angular/core';
import { DomSanitizer, SafeHtml, SafeResourceUrl, SafeScript, SafeStyle, SafeUrl } from '@angular/platform-browser';

type TransformType = 'html' | 'style' | 'script' | 'url' | 'resourceUrl';

type SafeType = SafeHtml | SafeStyle | SafeScript | SafeUrl | SafeResourceUrl;

/**
 * @deprecated old portals use only
 */
@Pipe({
  name: 'safe',
})
export class SafePipe implements PipeTransform {
  constructor(protected sanitizer: DomSanitizer) {}

  transform(value: string | number | undefined | null, type: 'html'): SafeHtml;
  transform(value: string | number | undefined | null, type: 'style'): SafeStyle;
  transform(value: string | number | undefined | null, type: 'script'): SafeScript;
  transform(value: string | number | undefined | null, type: 'url'): SafeUrl;
  transform(value: string | number | undefined | null, type: 'resourceUrl'): SafeResourceUrl;

  transform(value: string | number | undefined | null, type: TransformType): SafeType | string | null {
    const sanitizedItem: Record<TransformType, (item: string) => SafeType | string | null> = {
      html: (item: string) => this.sanitizer.sanitize(SecurityContext.HTML, item),
      style: (item: string) => this.sanitizer.sanitize(SecurityContext.STYLE, item),
      script: (item: string) => this.sanitizer.sanitize(SecurityContext.SCRIPT, item),
      url: (item: string) => this.sanitizer.sanitize(SecurityContext.URL, item),
      resourceUrl: (item: string) => this.sanitizer.sanitize(SecurityContext.RESOURCE_URL, item),
    };

    if (!(type in sanitizedItem)) {
      throw new Error(`Invalid safe type specified: ${type}`);
    }

    return sanitizedItem[type]((value ?? '').toString());
  }
}
