import { InjectionToken } from '@angular/core';

/**
 * @const InterceptLocale
 * Injection token for intercepting the current locale in interceptor.
 */
export const INTERCEPT_LANGUAGE: InjectionToken<InterceptLocale> = new InjectionToken('InterceptLocale');

export abstract class InterceptLocale {
  private _currentLang!: string;

  public get currentLang(): string {
    return this._currentLang;
  }
  public setLocale(locale: string): void {
    this._currentLang = locale;
  }
}
