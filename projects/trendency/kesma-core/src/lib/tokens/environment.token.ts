import { inject, InjectionToken, InjectOptions } from '@angular/core';

import { Environment } from '../definitions';

/**
 * @const AppEnvironment
 * Injection token for the environment interface to be provided by the applications.
 */
export const AppEnvironment: InjectionToken<Environment> = new InjectionToken('ENVIRONMENT');

export const injectEnvironment = () => inject(AppEnvironment);

export const injectEnvironmentWithOptions = (options: InjectOptions) => inject(AppEnvironment, options);
