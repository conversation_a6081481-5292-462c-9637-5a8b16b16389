import { Inject, Injectable, Optional } from '@angular/core';
import { HttpE<PERSON>, Http<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable } from 'rxjs';
import { StorageService, UtilService } from '../services';
import type { Request } from 'express';
import { INTERCEPT_LANGUAGE, InterceptLocale, REQUEST } from '../tokens';
import { DOCUMENT } from '@angular/common';

@Injectable({
  providedIn: 'root',
})
export class CommonHttpInterceptor implements HttpInterceptor {
  public defaultLanguage = 'hu';
  public defaultHeaders: Record<string, string> = {};

  constructor(
    private readonly storageService: StorageService,
    @Inject(REQUEST)
    @Optional()
    private readonly request: Request, // a szerver oldali REQUEST a server.ts-ből
    @Inject(INTERCEPT_LANGUAGE)
    @Optional()
    private readonly localeInterceptorService: InterceptLocale,
    @Inject(DOCUMENT) private readonly document: Document,
    private readonly utils: UtilService
  ) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    req = req.clone({ withCredentials: true });
    const locale = this.localeInterceptorService?.currentLang ?? this.defaultLanguage;
    const tokenName = 'jwt_token'; // az authentikációhoz használt, cookieban tárolt token neve

    // Kiszedi a tokent a cookie-k közül, attól függően hogy szerver vagy kliens oldalon vagyunk
    const cookies = (this.utils.isBrowser() ? this.document.cookie : this.request?.headers?.['cookie']) ?? '';
    const token = this.storageService.getCookie(tokenName, cookies);

    // Beállítja az Authorization és Locale headert a requestekben
    req = req.clone({
      setHeaders: {
        ...this.defaultHeaders,
        ...(token && { Authorization: token }),
        Locale: locale ? locale : '',
      },
    });

    return next.handle(req);
  }
}
