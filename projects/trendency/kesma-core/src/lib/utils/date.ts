import { addMinutes, format } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';
import { LOCAL_TIMEZONE, ZERO_DATE } from '../constants/date';

export const backendDateToDate = (dateString: string, zeroToNull = false): Date | undefined =>
  (zeroToNull && dateString === ZERO_DATE) || !dateString ? undefined : new Date(`${dateString.replace(' ', 'T')}Z`);

export const /**
   * Converts local time to UTC w/o the automatic TimeZone correction:
   * e.g. `17:00:00+02:00` => `17:00:00Z`, so the **+02:00** offset _WILL NOT_ be subtracted
   * from the local time. (i.e. "truncates" the full timezoned date to one "w/o timezone")
   *
   * @param date Input date in local timezone
   * @returns Date with same date+time values BUT like would have been originally in UTC (timezone = 'Z')
   */
  truncateToUtc = (date?: Date) => {
    if (!date) {
      return;
    }
    return new Date(format(date, 'yyyy-MM-dd HH:mm:ss')?.replace(' ', 'T') + 'Z');
  };

export const convertDateFromLocalToUTC = (localDate: Date): Date => {
  return addMinutes(localDate, localDate.getTimezoneOffset());
};

export const convertDateFromUTCToLocal = (utcDate: Date): Date => {
  return toZonedTime(utcDate, LOCAL_TIMEZONE);
};

export type ConversionOption = 'local' | 'utc';

export const toDate = (date: string | Date, convertTo: ConversionOption): Date => {
  switch (convertTo) {
    case 'local': {
      return typeof date === 'string' ? new Date(`${date.replace(' ', 'T')}Z`) : new Date(date.toISOString());
    }
    case 'utc': {
      return typeof date === 'string' ? new Date(`${date.replace(' ', 'T')}`) : date;
    }
  }
};
