export const canLazyLoad = typeof window !== 'undefined' && 'IntersectionObserver' in window;

export const isVisible = (el: HTMLElement): boolean => {
  if (!('getBoundingClientRect' in el)) {
    return true;
  }
  const { top, bottom, left, right } = el.getBoundingClientRect();
  const winRect = {
    top: window.scrollY,
    bottom: window.scrollY + window.innerHeight,
    left: window.scrollX,
    right: window.scrollX + window.innerWidth,
  };
  return bottom > winRect.top && top < winRect.bottom && right > winRect.left && left < winRect.right;
};
