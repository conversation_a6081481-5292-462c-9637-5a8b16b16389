// BASE64 encoding/decoding
// It also uses binary representation of the string, so that it can handle unicode characters correctly.

export const universalBtoa = (str: string): string => {
  try {
    return btoa(str);
  } catch (err) {
    return Buffer.from(str).toString('base64');
  }
};

export const universalAtob = (b64Encoded: string): string => {
  function decode(str: string) {
    return new TextDecoder().decode(Uint8Array.from(str, (m) => m.charCodeAt(0)));
  }
  try {
    return decode(atob(b64Encoded));
  } catch (err) {
    return decode(Buffer.from(b64Encoded, 'base64').toString());
  }
};
