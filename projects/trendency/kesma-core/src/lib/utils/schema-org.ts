export const SchemaOrgUtils = {
  scriptType: 'application/ld+json',
  className: '.structured-data',
};

export interface SchemaOrg {
  '@type': string;
}

export interface PublishableSchema extends SchemaOrg {
  datePublished?: Date | string;
  datePublishedString?: string;
  dateModified?: Date | string;
  dateModifiedString?: string;
}

export interface ArticleSchema extends PublishableSchema {
  headline?: string;
  alternativeHeadline?: string;
  image?: string;
  url: string;
  mainEntityOfPage?: string;
  description?: string;
  author?: string | ArticleAuthorSchema | ArticleAuthorSchema[];
  name?: string;
  keywords?: string;
  publisher: {
    '@type': 'Organization';
    name: string;
    logo?: {
      '@type'?: 'ImageObject';
      height?: string;
      width?: string;
      url?: string;
    };
    address?: [
      {
        '@type'?: 'PostalAddress';
        streetAddress?: string;
        addressLocality?: string;
        postalCode?: string;
        addressCountry?: string; // Alpha 2 country code
      },
    ];
    contactPoint?: {
      '@type'?: 'ContactPoint';
      telephone?: string;
      email?: string;
    };
  };
}

export interface ArticleAuthorSchema {
  '@type': string;
  name: string;
  jobTitle?: string;
  url?: string;
}

export interface BreadcrumbList extends SchemaOrg {
  '@type': 'BreadcrumbList';
  itemListElement: BreadcrumbListElement[];
}

export type BreadcrumbListElementItem = {
  '@id': string;
  name: string;
};

export type BreadcrumbListElement = {
  '@type': string;
  position: number;
  item: BreadcrumbListElementItem;
};

export const SchemaOrgWebpageDataTemplate: ArticleSchema = {
  '@type': 'WebPage',
  name: '',
  url: '',
  keywords: '', // TO BE FILLED

  publisher: {
    '@type': 'Organization',
    name: '', // TO BE FILLED
    logo: {
      '@type': 'ImageObject',
      height: '112',
      width: '112',
    },
  },
};

export interface PodcastEpisodeSchema extends PublishableSchema {
  '@type': 'PodcastEpisode';
  url: string;
  name: string;
  description: string;
}

export interface VideoObjectSchema extends SchemaOrg {
  '@type': 'VideoObject';
  name: string;
  thumbnailUrl: string;
  uploadDate: Date | string;
}

export interface LiveBlogPostingSchema extends SchemaOrg {
  '@type': 'LiveBlogPosting';
  '@id': string;
  thumbnailUrl: string;
  about: LiveBlogPostingAbout;
  coverageStartTime: Date | string;
  coverageEndTime: Date | string;
  headline: string;
  description: string;
  liveBlogUpdate: LiveBlogPost[];
}

export interface LiveBlogPostingAbout {
  '@type': 'Event';
  startDate: Date | string;
  name: string;
}

export interface LiveBlogPost {
  '@type': 'BlogPosting';
  headline: string;
  datePublished: Date | string;
  articleBody: string;
}

export interface ProfilePageSchema extends SchemaOrg {
  '@type': 'ProfilePage';
  mainEntity: {
    '@type': 'Person';
    name: string;
    url: string;
    image?: string;
    description?: string;
    sameAs?: string[];
  };
}

export interface ImageObjectSchema extends SchemaOrg {
  '@type': 'ImageObject';
  '@id': string;
  url: string;
  contentUrl: string;
  caption?: string;
  inLanguage?: string;
  height?: number;
  width?: number;
}
