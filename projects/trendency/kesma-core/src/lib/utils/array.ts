export const buildPhpArrayParam = <T>(input: T[], paramName: string): Record<string, T> =>
  input.reduce((res, value, index) => ({ ...res, [`${paramName}[${index}]`]: value }), {});

export const chunkArray = <T>(input: T[], blockLength: number, firstBlockLength = 0): T[][] => {
  const all = input.length - firstBlockLength;
  const chunks = Math.ceil(all / blockLength);
  const remainder = input.slice(firstBlockLength);

  return [input.slice(0, firstBlockLength)].concat(
    new Array(chunks)
      .fill(0)
      .reduce((sections, _, sliceIndex) => sections.concat([remainder.slice(sliceIndex * blockLength, (sliceIndex + 1) * blockLength)]), [])
  );
};
