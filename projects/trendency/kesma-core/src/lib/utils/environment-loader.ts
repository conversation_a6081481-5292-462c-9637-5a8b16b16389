import { EnvironmentSchema } from '../definitions';
import { z, ZodError, ZodSchema } from 'zod';
export const loadEnvironmentFromFile = async (filePath: string): Promise<any> => {
  try {
    const res = await fetch(filePath);
    return await res.json();
  } catch (e) {
    console.error('❗❗❗ Fatal Error! Unable to load environment.json ❗❗❗');
    throw e;
  }
};

export function validateAndCast<T extends ZodSchema<any>>(obj: unknown, schema: T): ReturnType<T['parse']> {
  return schema.parse(obj);
}

const load = async (schema: ZodSchema): Promise<z.infer<typeof schema>> => {
  const envJson = await loadEnvironmentFromFile('environment.json');
  return validateEnvironmentWithCustomSchema(envJson, schema) as z.infer<typeof schema>;
};
const validate = <T, O>(envJson: any, validationSchema: z.ZodType<T, z.ZodTypeDef, O>): z.infer<typeof validationSchema> => {
  try {
    return validationSchema.parse(envJson);
  } catch (e) {
    console.error('❗❗❗ Fatal Error! Failed to load environment.json ❗❗❗');
    if (e instanceof ZodError) {
      console.error('Issues: ', e.issues);
      console.error('Invalid environment: ', envJson);
    }
    throw e;
  }
};

export const loadEnvironment = async () => load(EnvironmentSchema) as Promise<z.infer<typeof EnvironmentSchema>>;
export const loadEnvironmentWithCustomSchema = async <Schema extends ZodSchema = typeof EnvironmentSchema>(
  validationSchema: Schema
): Promise<z.infer<typeof validationSchema>> => load(validationSchema);

export const validateEnvironment = (envJson: any) => validate(envJson, EnvironmentSchema) as z.infer<typeof EnvironmentSchema>;
export const validateEnvironmentWithCustomSchema = <T, O>(envJson: any, validationSchema: z.ZodType<T, z.ZodTypeDef, O>) =>
  validate(envJson, validationSchema) as z.infer<typeof validationSchema>;
