import { getPrimaryColumnColorComboByColumnTitleColor } from './article-card-utils';
import { Article, BackendVideo, Video, VideoCard } from '../definitions';

export const videoToArticle = (
  { title, lead, columnTitle, columnSlug, slug, tags, publishDate, description, length, thumbnail }: Video,
  columnBgColor: string | null = null
): Article =>
  (title
    ? {
        title,
        lead,
        primaryColumn: { title: columnTitle, slug: columnSlug },
        primaryColumnColorCombo: getPrimaryColumnColorComboByColumnTitleColor(columnBgColor),
        slug,
        tags: tags ?? [],
        publishDate,
        columnSlug,
        columnTitle,
        excerpt: description,
        length: parseInt(length as string),
        isVideoType: true,
        thumbnail,
        category: {
          name: columnTitle,
          slug: columnSlug,
        },
      }
    : ({} as unknown)) as Article;

export const videoCardToArticle = (
  { title, lead, columnTitle, columnSlug, slug, tags, publishDate, description, length, coverImage }: VideoCard,
  columnBgColor: string | null = null
): Article =>
  (title
    ? {
        title,
        lead,
        primaryColumn: { title: columnTitle, slug: columnSlug },
        primaryColumnColorCombo: getPrimaryColumnColorComboByColumnTitleColor(columnBgColor),
        slug,
        tags: tags ?? [],
        publishDate,
        columnSlug,
        columnTitle,
        excerpt: description,
        length: parseInt(length as string),
        isVideoType: true,
        thumbnail: coverImage,
        category: {
          name: columnTitle,
          slug: columnSlug,
        },
      }
    : ({} as unknown)) as Article;

export const videoToVideoCard = (
  { title, columnTitle, columnSlug, slug, columnId, length, thumbnail, publishDate, description, lead, tags }: Video,
  categoryColor?: string
): VideoCard =>
  title
    ? {
        title,
        length,
        coverImage: thumbnail,
        columnId,
        columnSlug,
        columnTitle,
        publishDate,
        thumbnail: {
          url: thumbnail ?? '',
          alt: 'Videó: ' + title,
          url43AspectRatio: thumbnail,
        },
        description,
        slug,
        tags,
        lead,
        category: {
          name: columnTitle,
          slug: columnSlug,
          color: categoryColor,
        },
      }
    : ({} as VideoCard);

export function backendVideoToVideoCard(backendVideo: BackendVideo): VideoCard {
  return {
    id: backendVideo.id,
    title: backendVideo.title,
    length: backendVideo.length,
    coverImage: backendVideo.coverImageUrl,
    lead: backendVideo.lead,
    slug: backendVideo.slug,
    thumbnail: {
      url: backendVideo.coverImage?.thumbnailUrl ?? '',
    },
    category: {
      ...backendVideo.column,
    },
    columnId: backendVideo.column?.id,
    columnTitle: backendVideo.column?.title,
    columnSlug: backendVideo.column?.slug,
    publishDate: backendVideo.publicDate,
    coverImageFocusedImages: backendVideo.coverImage?.focusedImages,
    thumbnailFocusedImages: backendVideo.coverImage?.focusedImages,
  };
}
