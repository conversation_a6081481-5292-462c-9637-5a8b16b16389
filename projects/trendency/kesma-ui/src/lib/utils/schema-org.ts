import {
  ArticleAuthorSchema,
  ArticleSchema,
  backendDateToDate,
  ImageObjectSchema,
  LiveBlogPost,
  LiveBlogPostingSchema,
  PodcastEpisodeSchema,
  ProfilePageSchema,
  SchemaOrg,
  VideoObjectSchema,
} from '@trendency/kesma-core';
import { Article, ArticleBodyType, BackendAuthorData } from '../definitions';

export function getStructuredDataForVideos(article?: Article, document?: Document): SchemaOrg[] {
  if (!article?.body) {
    return [];
  }
  const result: SchemaOrg[] = [];
  for (const element of article.body) {
    // With custom video elements
    const elementDetails = element.details[0];
    if (element.type === ArticleBodyType.MediaVideo) {
      const video = elementDetails.value;
      if (video && video?.title) {
        // Only the title is required in the structure
        result.push({
          '@type': 'VideoObject',
          name: video.title,
          description: video.description || '',
          thumbnailUrl: video.thumbnailUrl || '',
          uploadDate: new Date(video.uploadedAt || Date.now()).toISOString(),
          embedUrl: video.videaUrl || '', // YT videos will use this prop too
        } as SchemaOrg);
      }
    }
  }

  const iframes = document?.querySelectorAll('.block-content iframe') as NodeListOf<HTMLIFrameElement>;
  const videoPattern = /.+(?:youtu|yt|videa).+/gi;
  if (!iframes?.length) {
    return result;
  }
  iframes.forEach((iframe) => {
    if (new RegExp(videoPattern).exec(iframe.src)) {
      const srcAsArray = iframe.src.split('/');
      const videoEmbedId = srcAsArray[srcAsArray.length - 1]?.split('?')?.[0];
      result.push({
        '@type': 'VideoObject',
        name: article.title,
        description: article.excerpt,
        thumbnailUrl: videoEmbedId ? `https://img.youtube.com/vi/${videoEmbedId}/maxresdefault.jpg` : null,
        uploadDate: article.publishDate?.toISOString(),
        embedUrl: iframe.src,
      } as SchemaOrg);
    }
  });
  return result;
}

export const getStructuredDataForArticle = (article: Article, currentUrl: string, siteUrl: string, params?: Record<string, any>): ArticleSchema => {
  const authors = getArticleAuthors(article, siteUrl, params);
  return (
    article && {
      '@type': 'NewsArticle',
      headline: article.title,
      alternativeHeadline: article.title,
      image: article.thumbnail,
      url: currentUrl,
      mainEntityOfPage: currentUrl,
      description: article.excerpt,
      dateModified: article.lastUpdated ?? article.publishDate,
      dateModifiedString: article.rawLastUpdated ?? article.rawPublishDate,
      author: authors.length > 1 ? authors : authors[0],
      datePublished: article.publishDate,
      datePublishedString: article.rawPublishDate,
      publisher: getSchemaOrgPublisher(siteUrl),
    }
  );
};
export const getArticleAuthors = (
  article: Article,
  siteUrl: string,
  params?: { hasAuthorPage?: boolean; hasAuthorPageSlug?: boolean }
): ArticleAuthorSchema[] => {
  const authors: ArticleAuthorSchema[] = [];
  if (article && 'publicAuthorM2M' in article && article?.publicAuthorM2M && article?.publicAuthorM2M?.length > 0) {
    article.publicAuthorM2M.map((publicAuthor) => {
      authors.push({
        '@type': 'Person',
        name: publicAuthor.fullName || `${publicAuthor.firstName} ${publicAuthor.lastName}`,
        jobTitle: 'Journalist',
        ...((params?.['hasAuthorPage'] || params?.['hasAuthorPageSlug']) && { url: `${siteUrl}/szerzo/${publicAuthor.slug}` }),
      });
    });
  } else {
    // Legacy logic for portals that do not use the Public Authors M2M.
    authors.push({
      '@type': 'Person',
      name: article.publicAuthor || '',
      jobTitle: 'Journalist',
      ...(params?.['hasAuthorPage'] && article?.publicAuthor && { url: `${siteUrl}/szerzo/${article.publicAuthor}` }),
      ...(params?.['hasAuthorPageSlug'] && article?.publicAuthorSlug && { url: `${siteUrl}/szerzo/${article.publicAuthorSlug}` }),
    });
  }
  return authors;
};

export const getSchemaOrgPublisher = (siteUrl: string): SchemaOrgPublisher => ({
  '@type': 'Organization',
  name: 'Mediaworks Hungary Zrt.',
  logo: {
    '@type': 'ImageObject',
    height: '46',
    width: '227',
    url: `${siteUrl}/assets/images/mw/mwf_logo_mediaworks.png`,
  },
});

export interface SchemaOrgPublisher {
  '@type': 'Organization';
  name: string;
  logo?: {
    '@type'?: 'ImageObject';
    height?: string;
    width?: string;
    url?: string;
  };
}

export const getSchemaPodcastEpisode = (article: Article, currentUrl: string): PodcastEpisodeSchema => ({
  '@type': 'PodcastEpisode',
  name: article.title,
  url: currentUrl,
  datePublished: article.publishDate,
  dateModified: article.lastUpdated ?? article.publishDate,
  description: article?.excerpt || article?.lead,
});

export const getSchemaVideoObject = (article: Article): VideoObjectSchema => ({
  '@type': 'VideoObject',
  name: article.title,
  uploadDate: article.publishDate as Date,
  thumbnailUrl: article.thumbnail as string,
});

export const getSchemaLiveBlogPosting = (article: Article, currentUrl: string): LiveBlogPostingSchema => ({
  '@type': 'LiveBlogPosting',
  '@id': currentUrl,
  thumbnailUrl: article.thumbnail as string,
  about: {
    '@type': 'Event',
    startDate: article.publishDate as Date,
    name: article.title,
  },
  coverageStartTime: article.minuteToMinuteBlocks?.length
    ? (backendDateToDate(article.minuteToMinuteBlocks[article.minuteToMinuteBlocks!.length - 1].date.toString()) as Date)
    : new Date(),
  coverageEndTime: article.minuteToMinuteBlocks?.length ? (backendDateToDate(article.minuteToMinuteBlocks?.[0].date.toString()) as Date) : new Date(),
  headline: article.title,
  description: article.excerpt || article.lead,
  liveBlogUpdate: article.minuteToMinuteBlocks?.map((post) => ({
    '@type': 'BlogPosting',
    headline: post.title,
    datePublished: backendDateToDate(post.date.toString()) as Date,
    articleBody: post.body as unknown as string,
  })) as LiveBlogPost[],
});

export const getStructuredDataForProfilePage = (author: BackendAuthorData, siteUrl: string): ProfilePageSchema => ({
  '@type': 'ProfilePage',
  mainEntity: {
    '@type': 'Person',
    name: author?.publicAuthorName || author?.public_author_name || '',
    url: `${siteUrl}/szerzo/${author.slug}`,
    image: author?.avatar?.bigUrl || author?.avatar?.fullSizeUrl || '',
    description: author?.publicAuthorDescription || author?.public_author_description || '',
    sameAs: [author?.facebook, author?.youtube, author?.tiktok, author?.pinterest, author?.instagram].filter(Boolean),
  },
});

export function getSchemaImageObject(url: string, additionalProperties: Partial<ImageObjectSchema> = {}): ImageObjectSchema {
  // All of these are required: https://developer.yoast.com/features/schema/pieces/image/
  return {
    '@type': 'ImageObject',
    '@id': url,
    contentUrl: url,
    url,
    ...additionalProperties,
  };
}
