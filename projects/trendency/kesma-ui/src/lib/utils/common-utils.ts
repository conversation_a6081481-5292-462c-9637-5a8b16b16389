import { ComponentRef } from '@angular/core';

import { addMinutes, format } from 'date-fns';
import { MonoTypeOperatorFunction, Observable, takeUntil } from 'rxjs';
import {
  Article,
  ArticleCard,
  BackendBrandingBoxType,
  BackendVideo,
  BasicDossier,
  BasicDossierArticle,
  BreakingBlock,
  DossierArticle,
  DossierArticleShort,
  FakeBool,
  GalleriesResponse,
  GalleryData,
  LayoutArticleData,
  RecipeCard,
  Video,
  VideoCard,
} from '../definitions';

export const ZERO_DATE = '0000-00-00 00:00:00';

export const buildPhpArrayParam = <T>(input: T[], paramName: string): Record<string, T> =>
  input.reduce((res, value, index) => ({ ...res, [`${paramName}[${index}]`]: value }), {});

export const backendDateToUTC = (dateString: string, zeroToNull = false): string | null =>
  (zeroToNull && dateString === ZERO_DATE) || !dateString ? null : `${dateString.replace(' ', 'T')}Z`;

export const backendDateToDate = (dateString: string, zeroToNull = false): Date | null =>
  (zeroToNull && dateString === ZERO_DATE) || !dateString ? null : new Date(`${dateString.replace(' ', 'T')}Z`);

export const strBoolToBool = (strBool: string | number | boolean): boolean => !!((strBool as number) * 1);

export const /**
   * Converts local time (like 17:00:00+02:00 => 17:00:00Z)
   * !!!Important!!! the TimeZone WILL NOT be compensated,
   * eg. a +02:00 offset WILL NOT be subtracted from the local time
   *
   * @example
   * Used to compare time from BE (eg. '2021-08-01 17:00:00') to browser time ('2021-08-01T17:00:00+02:00')
   * as we convert BE time to '2021-08-01T17:00:00Z' so we need to "truncate" the timezone from browser time,
   * which if it would be converted to UTC would become '2021-08-01T15:00:00Z';
   * instead this function will return '2021-08-01T17:00:00Z' so the two dates will be safely comparable
   * @param date Input date in local timezone
   * @returns Date with same date+time values BUT like would have been originally in UTC (timezone = 'Z')
   */
  truncateToUtc = (date?: Date) => {
    if (!date) {
      return;
    }
    return new Date(format(date, 'yyyy-MM-dd HH:mm:ss')?.replace(' ', 'T') + 'Z');
  };

/**
 * Converts a local Date object to UTC-0 timezone and formats to a timestamp string that can be used at BE filters.
 * @param date
 */
export const convertDateToBackendUtcDate = (date: Date): string => {
  date = addMinutes(date.getTime(), date.getTimezoneOffset());
  return format(date, "yyyy-MM-dd'T'HH:mm:ss");
};

export const chunkArray = <T>(input: T[], blockLength: number, firstBlockLength = 0): T[][] => {
  const all = input.length - firstBlockLength;
  const chunks = Math.ceil(all / blockLength);
  const remainder = input.slice(firstBlockLength);

  return [input.slice(0, firstBlockLength)].concat(
    new Array(chunks)
      .fill(0)
      .reduce((sections, _, sliceIndex) => sections.concat([remainder.slice(sliceIndex * blockLength, (sliceIndex + 1) * blockLength)]), [])
  );
};

export const extractIds = <T = string>(data: { id?: T }[]): T[] => ((data || []).filter((elem) => !!elem?.id) as { id: T }[]).map(({ id }) => id);

export const buildArticleUrl = (
  article: Article | ArticleCard | BasicDossierArticle | DossierArticle | DossierArticleShort | LayoutArticleData,
  category?: string,
  isOpinionArticle?: boolean,
  brandingType?: string,
  isUndated?: boolean
): string[] => {
  if (!article) {
    return [];
  }

  if ((article as ArticleCard).contentType === 'gallery' && article.slug) {
    return ['/', 'galeria', article.slug];
  } else {
    let pubDate = (article.publishDate as any)?.date ?? article.publishDate;
    if (typeof pubDate === 'string') {
      pubDate = backendDateToDate(pubDate);
    }
    const categorySlug = category ?? article.columnSlug ?? (article as Article).primaryColumn?.slug;

    if (!pubDate || (!categorySlug && !isOpinionArticle && !brandingType) || !article.slug) {
      return ['/', '404'];
    }

    const [year, month] = pubDate.toISOString().substring(0, 7).split('-');

    const publishYear = (article as ArticleCard).publishYear ? (article as ArticleCard).publishYear + '' : year;
    let publishMonth = (article as ArticleCard).publishMonth ? (article as ArticleCard).publishMonth + '' : month;

    // If month is not padded with 0, pad it with 0 (wrong data can come from API as publishMonth)
    if (publishMonth.length === 1) {
      publishMonth = '0' + publishMonth;
    }

    const brandingSlug = BackendBrandingBoxType[brandingType?.toLowerCase() as string] || brandingType;
    if (brandingType && brandingSlug) {
      return ['/', 'brand', brandingSlug, publishYear, publishMonth, article.slug];
    }

    if ((article as ArticleCard).foundationTagSlug) {
      return ['/', (article as ArticleCard).foundationTagSlug ?? ''];
    }

    if (isUndated) {
      return ['/', categorySlug, article.slug];
    }

    return ['/', categorySlug, publishYear, publishMonth, article.slug];
  }
};

export const buildRecipeUrl = (recipe: RecipeCard): string[] => {
  if (!recipe) {
    return ['/', '404'];
  }

  return ['/', 'recept', recipe?.slug as string];
};

export const buildTagUrl = (article: Article | ArticleCard | DossierArticle | DossierArticleShort | VideoCard, selectedTag = 0): string[] => {
  const slug = (article as ArticleCard)?.tags?.[selectedTag]?.slug;
  return slug ? ['/', 'cimke', slug] : [];
};
export const buildVideoUrl = (videoCard: VideoCard | BackendVideo): string[] => {
  const slug = (videoCard as VideoCard)?.slug;
  return slug ? ['/', 'video', slug] : [];
};

export const buildColumnUrl = (article: Article | ArticleCard | DossierArticle | DossierArticleShort | Video | VideoCard | BackendVideo): string[] => {
  const slug = (article as ArticleCard)?.category?.slug ?? (article as ArticleCard)?.columnSlug ?? (article as BackendVideo)?.column?.slug;
  return slug ? ['/', 'rovat', slug] : [];
};

export const buildAuthorUrl = (article: Article | ArticleCard | RecipeCard): string[] => {
  const slug = (article as ArticleCard)?.author?.slug || (article as RecipeCard)?.publicAuthor?.slug;
  return slug ? ['/', 'szerzo', slug] : [];
};

export const buildRegionUrl = (article: Article | ArticleCard | DossierArticle | DossierArticleShort, selectedRegion = 0, overrideUrl?: string): string[] => {
  const slug = (article as ArticleCard)?.regions?.[selectedRegion]?.slug ?? article?.regionSlug;
  return slug ? ['/', overrideUrl || 'regio', slug] : [];
};

export const buildGalleryUrl = (gallery: GalleriesResponse | GalleryData): string[] => {
  const slug = (gallery as GalleryData)?.slug;
  return slug ? ['/', 'galeria', slug] : [];
};

export const buildDossierUrl = (dossier: BasicDossier): string[] => {
  const slug = dossier?.slug;
  return slug ? ['/', 'dosszie', slug] : [];
};

export const buildUrl = (inputText: string, inputData: UrlSource): string | string[] => {
  if (inputText.match(/^\w+\.\w+$/)) {
    // like origo.hu
    return inputText;
  }
  const [protocol, ...parts] = (inputText ?? 'https://').replace('//', '').split(':');
  switch (protocol) {
    case 'http':
    case 'https':
    case 'ftp':
    case 'sftp':
      return inputText; // external url
    case 'column':
      return buildColumnUrl(inputData as Article);
    case 'article':
      return buildArticleUrl(inputData as Article);
    case 'dossier':
      return buildDossierUrl(inputData as BasicDossier);
    case 'breaking':
      return buildArticleUrl(inputData as Article);
    case 'gallery':
      return buildGalleryUrl(inputData as GalleryData);
    case 'region':
      return buildRegionUrl(inputData as Article);
    default:
      console.warn('Unable to build url: no protocol specified!', inputData, inputText);
      return inputText;
  }
};

export const buildMatchUrl = (match: { slug: string; [key: string]: any }) => {
  if (!match.slug) {
    return ['/', '404'];
  }
  return ['/', 'merkozes', match.slug, 'elo'];
};

export const buildCompetitionUrl = (competition: { slug: string; [key: string]: any }) => {
  if (!competition.slug) {
    return ['/', '404'];
  }
  return ['/', 'bajnoksag', competition.slug];
};

export const buildTeamUrl = (league: { slug: string; [key: string]: any }, team: { slug: string; [key: string]: any }) => {
  if (!team?.slug || !league?.slug) {
    return ['/', '404'];
  }
  return ['/', 'csapat', team.slug, league.slug];
};

export type UrlSource =
  | BasicDossier
  | GalleriesResponse
  | GalleryData
  | Article
  | ArticleCard
  | DossierArticle
  | DossierArticleShort
  | BasicDossierArticle
  | LayoutArticleData
  | VideoCard
  | BreakingBlock;

export function untilDestroyed<T>(componentRef: ComponentRef<T> & { instance: T & { destroy$: Observable<void> } }): MonoTypeOperatorFunction<unknown> {
  return takeUntil(componentRef.instance.destroy$);
}

export const backendBoolToBool = (val: boolean | number | FakeBool | undefined | null): boolean | undefined | null => {
  if (typeof val === 'undefined' || typeof val === null || typeof val === 'boolean') return val as undefined;
  else if (typeof val === 'number') {
    return val === 1;
  } else if (typeof val === 'string') {
    return val === '1';
  } else return undefined;
};

export const removeHTMLTagsFromString = (htmlString: string = ''): string => {
  // Remove HTML elements
  let textContent = htmlString.replace(/(<([^>]+)>)/gi, ' ');
  // Remove multiple spaces
  textContent = textContent.replace(/\s+/g, ' ');
  // Trim starting and ending whitespace
  return textContent.trim();
};
