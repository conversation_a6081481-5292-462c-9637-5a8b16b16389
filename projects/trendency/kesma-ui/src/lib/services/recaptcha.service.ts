import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { ReCaptchaV3Service } from 'ngx-captcha';
import { injectEnvironment } from '@trendency/kesma-core';

/**
 * Handles recaptcha execution and queueing.
 *
 * The queue is needed because only one recaptcha can be executed at a time, and it is not handled in ReCaptchaV3Service.
 */
@Injectable({
  providedIn: 'root',
})
export class RecaptchaService {
  readonly #queue: (() => void)[] = [];
  private readonly environment = injectEnvironment();

  constructor(private readonly reCaptchaV3Service: ReCaptchaV3Service) {}

  /**
   * Executes the callback after a recaptcha token is acquired. It's best to use this with synchronous code. With async code, it's better to use `acquire` function.
   * If there is a recaptcha currently running, the callback will be queued.
   * @param action Recaptcha action name.
   * @param callback The callback to execute after the recaptcha token is acquired. As a second parameter, it can receive a Subject, to signal when the callback is finished.
   * @returns An observable that completes when `callback` is finished. The only pushed value is the return value of `callback`.
   * @remarks If the callback throws an error, the observable will error too, but the queue will continue.
   */
  enqueue<TRes = unknown>(action: string, callback: (token: string) => TRes): Observable<TRes> {
    let isHalted = this.#queue.length === 0;
    const callbackFinished$ = new Subject<TRes>();
    this.#queue.push(this.getQueueCallback(action, callback, callbackFinished$));
    if (isHalted) {
      this.#queue[0]();
    }

    return callbackFinished$.asObservable();
  }

  /**
   *  Used to pipe in to an observable, to wait for the next recaptcha window.
   *  @param action Recaptcha action name.
   *  @returns An observable that pushes the recaptcha token when it is acquired, and no other tasks are running then completes.
   *  @remarks If the callback throws an error, the observable will error too, but the queue will continue.
   *  @example
   *  ```ts
   *  this.recaptchaService.whenReady().pipe(
   *    switchMap((token) => this.voteService.onVotingSubmit(token, voteData))
   *  )
   */
  acquire(action: string): Observable<string> {
    const res$ = new Subject<string>();
    this.enqueue(action, (token) => {
      res$.next(token);
      res$.complete();
    });
    return res$.asObservable();
  }

  private getQueueCallback<TRes = unknown>(action: string, callback: (token: string) => TRes, callbackFinished$: Subject<TRes>) {
    return () => {
      callbackFinished$.subscribe(() => this.callNext());
      this.withRecaptcha(action, callback, callbackFinished$);
    };
  }

  private callNext() {
    this.#queue.shift();
    if (!this.#queue.length) return;

    this.#queue[0]();
  }

  private withRecaptcha<TRes>(action: string, callback: (token: string) => TRes, callbackFinished$: Subject<TRes>) {
    if (!this.environment.googleSiteKey) {
      throw new Error('Google Site Key is not provided. Either provide it in the app or pass it as a parameter to the method.');
    }
    this.reCaptchaV3Service.execute(
      this.environment.googleSiteKey,
      action,
      (token: string) => {
        try {
          callbackFinished$.next(callback(token));
          callbackFinished$.complete();
        } catch (e) {
          console.error('[RecaptchaService::enqueue] Task failed', e);
        }
      },
      {
        useGlobalDomain: false,
      },
      (err) => {
        console.error('reCaptchaV3Service.execute error', err);
        callbackFinished$.error(err);
      }
    );
  }
}
