import { DOCUMENT } from '@angular/common';
import { Inject, Injectable, Optional } from '@angular/core';
import { ArticleBody, ArticleBodyDetailType, ArticleBodyType } from '../definitions';
import { ActivatedRoute } from '@angular/router';

export const HEADINGS_v2 = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
export const LISTS_v2 = ['ul', 'ol'];
export const SPECIAL_TAGS_v2 = ['span'];
export const TO_SKIP_TAGS_v2 = [...HEADINGS_v2, ...LISTS_v2, ...SPECIAL_TAGS_v2];
export const ALL_TEXT_TAGS_V2 = ['p', ...TO_SKIP_TAGS_v2];

/**
 * Enhanced version of AutoArticleBodyAdService that places ads every 5 elements
 * (paragraphs, highlighted sections, images) with up to 6 zones for extra long articles.
 * Uses new banner naming convention: box_X, mobilrectangle_X, roadblock_X
 */
@Injectable()
export class AutoArticleBodyAdV2Service {
  structure: ArticleBody[] = [];
  /**
   * Buffer to store iterated wysiwyg element before they are added to a wysiwyg component.
   */
  wysiwygBuffer: Element[] = [];

  /**
   * Constructed body that will contain the original body with the inserted ads.
   */
  constructedBody: ArticleBody[] = [];

  enableDebug = false;

  constructor(
    @Inject(DOCUMENT) private readonly document: Document,
    @Optional() private activatedRoute: ActivatedRoute
  ) {}

  debug(...args: unknown[]): void {
    if (this.enableDebug) {
      console.warn(args[0], ...args.slice(1));
    }
  }

  init(body: ArticleBody[]): void {
    if (!this.enableDebug && this.activatedRoute?.snapshot?.queryParams['interrupter_debug'] !== undefined) {
      this.enableDebug = true;
    }

    if (!body) {
      return;
    }

    this.structure = this.createStruct(body);

    this.debug('1. Created raw struct: ', this.structure);

    const removeEadverts = this.structure.filter((item) => item?.type !== ArticleBodyType.Advert) as unknown as Element[];

    this.debug('2. Cleaned EAdverts from struct: ', removeEadverts);

    this.structure = this.addAdverts(removeEadverts);
  }

  createStruct(body: ArticleBody[]) {
    return body.reduce((acc, component: ArticleBody) => {
      if (this.isWysiwygComponent(component)) {
        const root = this.document.createElement('div');
        root.innerHTML = component.details[0].value;
        // Not a real-world use-case but we need to handle plain text in the value. Sometimes migrated contents
        // can have plain text values which would display on the portal. We need to wrap these in a p tag to create valid html elements.
        if (component.details[0]?.value.length > 0 && root.childElementCount === 0) {
          root.innerHTML = `<p>${component.details[0]?.value.trim()}</p>`;
        }
        const elements = Array.from(root.children);
        return (acc = [...acc, ...elements] as ArticleBody[]);
      } else {
        return (acc = [...acc, component]);
      }
    }, [] as ArticleBody[]);
  }

  /**
   * Runs the whole automatic ad insertion logic based on the body and config provided in the init() function.
   */
  autoAd(): ArticleBody[] {
    this.constructedBody = [];
    this.structure.map((_item, index) => {
      this.iterate(index);
    });

    this.debug('4. Created body: ', this.constructedBody);

    return this.constructedBody;
  }

  /**
   * Enhanced ad insertion logic that counts elements (paragraphs, highlighted sections, images)
   * and inserts ads every 5 elements, up to 6 zones maximum.
   */
  addAdverts(structure: Element[]): ArticleBody[] {
    const unfilteredWithAdverts: {
      element: ArticleBody | Element;
      elementCount: number;
      textLength: number;
      type: 'advert' | 'non-text' | 'empty' | 'text';
    }[] = [];
    let elementCount = 0;
    const elementThresholds = [4, 9, 14, 19, 24, 29]; // Every 5 elements, up to 6 zones
    let thresholdIndex = 0;

    structure.forEach((element, i) => {
      const trimmedElementText = element?.textContent?.trim()?.length ?? 0;
      const nextTrimmedElementText = structure[i + 1]?.textContent?.trim()?.length ?? 0;

      const remainingElementsInTheBody = 3;
      const isCurrentIndexInTheEndOfBody = structure?.filter((element) => element?.textContent?.trim()?.length).length - i < remainingElementsInTheBody;
      const isNextParagraphElementLongEnough =
        isCurrentIndexInTheEndOfBody && this.getTagName(structure[i + 1]) === 'p' ? (structure[i + 1]?.textContent?.trim()?.length ?? 0) >= 100 : true;

      // Count content elements (paragraphs, images, highlighted sections) - even if empty
      if (this.isContentElement(element)) elementCount++;

      unfilteredWithAdverts.push({
        element,
        elementCount: elementCount,
        textLength: trimmedElementText,
        type: !trimmedElementText ? 'empty' : !this.isTextContent(element) ? 'non-text' : 'text',
      });

      const isImage =
        this.getTagName(structure[i + 1]) === 'figure' &&
        structure[i + 1]?.className?.includes('image') &&
        !structure[i]?.className?.includes('image-style-align-left') &&
        !structure[i]?.className?.includes('image-style-align-right');

      const isNextParagraph =
        isNextParagraphElementLongEnough && nextTrimmedElementText && (['p', ...HEADINGS_v2].includes(this.getTagName(structure[i + 1])) || isImage);

      // Replace character threshold with element threshold
      const isAtElementThreshold = elementCount >= elementThresholds[thresholdIndex];

      const isPreviousNotSpanQuoteHighlight = trimmedElementText ? true : this.getTagName(structure[i - 1]) !== SPECIAL_TAGS_v2[0]; //span

      if (
        isAtElementThreshold &&
        thresholdIndex < elementThresholds.length &&
        (!this.isElementInList(element, TO_SKIP_TAGS_v2)
          ? !this.isElementInList(structure[i + 1], [...LISTS_v2, ...SPECIAL_TAGS_v2])
          : !this.isTextContent(structure[i + 1])) &&
        isPreviousNotSpanQuoteHighlight &&
        isNextParagraph
      ) {
        const advert = this.createEadvert();
        // Update the DIV ID with the correct zone number
        advert.details[0].value = `<div id="Mandiner_normal_content_${thresholdIndex + 1}"></div>`;
        unfilteredWithAdverts.push({ element: advert, elementCount: 0, textLength: 0, type: 'advert' });
        thresholdIndex++;

        this.debug(`Inserted ad zone ${thresholdIndex} after ${elementCount} elements`);
      }
    });

    this.debug('3. Added adverts to struct: ', unfilteredWithAdverts);

    return unfilteredWithAdverts.map((item) => item.element) as ArticleBody[];
  }

  /**
   * Determines if an element is a content element (paragraph, highlighted section, image)
   */
  private isContentElement(element: Element): boolean {
    if (!element) return false;

    const tagName = this.getTagName(element);
    const hasText = element?.textContent?.trim()?.length > 0;

    // Paragraphs (with or without text - empty paragraphs still count)
    if (tagName === 'p') {
      return true;
    }

    // Images (figure elements with image class)
    if (tagName === 'figure' && element.className?.includes('image')) {
      return true;
    }

    // Highlighted sections (spans with content)
    if (tagName === 'span' && hasText) {
      return true;
    }

    return false;
  }

  isWysiwygComponent(component: ArticleBody): boolean {
    return component?.type === ArticleBodyType.Wysywyg;
  }

  getTagName(element: Element): string {
    return element?.tagName?.toLowerCase() || '';
  }

  isElementInList(element: Element, list: string[]): boolean {
    return list.includes(this.getTagName(element));
  }

  /**
   * Determines whether the given element should be treated as text element. This checks based on the provided config.
   */
  isTextContent(item: Element): boolean {
    return item instanceof Element && ALL_TEXT_TAGS_V2.includes(item.tagName?.toLowerCase());
  }

  getIteratorParams(structIndex: number): Record<'next' | 'current', ArticleBody | null> {
    const next = structIndex - 1 < this.structure.length ? this.structure[structIndex + 1] : null;
    const current = this.structure[structIndex] as ArticleBody;
    return {
      next,
      current,
    };
  }

  /**
   * Called in each iteration.
   * This function checks whether the current element should be treated as a paragraph and increments the paragraph counter.
   * It also handles to push the components to the new body based on their type.
   */
  iterate(structIndex: number): void {
    const { next, current } = this.getIteratorParams(structIndex);

    if (current instanceof Element) {
      this.wysiwygBuffer.push(current);
      if (!(next instanceof Element)) {
        this.flushWysiwygBuffer();
      }
    } else {
      this.constructedBody.push(current as ArticleBody);
    }
  }

  /**
   * Flushes the wysiwyg buffer.
   * The wysiwyg buffer is used to store elements that should be then used in a wysiwyg component.
   * This function flushes the buffer and the items that are
   * in the buffer then used to create a wysiwyg component that is inserted to the new body.
   */
  flushWysiwygBuffer(): void {
    if (this.wysiwygBuffer.length === 0) {
      return;
    }

    const wysiwygComponent = this.createWysiwygComponent(this.wysiwygBuffer);
    this.wysiwygBuffer = [];
    this.constructedBody.push(wysiwygComponent);
  }

  /**
   * Creates a WYSIWYG component based on the DOM elements that are passed into the input.
   * @param children elements that should be inserted into the WYSIWYG component
   */
  createWysiwygComponent(children: Element[]): ArticleBody {
    const root = this.document.createElement('div');
    children.map((node) => root.appendChild(node));
    const id = Math.floor(Math.random() * 10000);
    return {
      id: 'auto_wysiwyg_' + id,
      type: ArticleBodyType.Wysywyg,
      subComponents: [],
      details: [
        {
          key: 'text',
          type: ArticleBodyDetailType.WysywygDetail,
          value: root.innerHTML,
        },
      ],
    };
  }

  /**
   * Creates an Eadvert component.
   * This is used to create the ads that are inserted into the body.
   * Creates DIV with ID pattern: Mandiner_normal_content_X
   */
  createEadvert(): ArticleBody {
    const id = Math.floor(Math.random() * 10000);
    // We'll use a counter to track zone numbers, but for now create a generic structure
    return {
      id: 'auto_ad_' + id,
      type: ArticleBodyType.Advert,
      subComponents: [],
      details: [
        {
          type: ArticleBodyDetailType.AdvertDetail,
          key: 'text',
          value: '', // This will be populated with the correct DIV ID during processing
        },
      ],
    };
  }
}
