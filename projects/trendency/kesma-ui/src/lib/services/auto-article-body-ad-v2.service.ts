import { DOCUMENT } from '@angular/common';
import { Inject, Injectable, Optional } from '@angular/core';
import { ArticleBody, ArticleBodyDetailType, ArticleBodyType, AdvertisementBannerName, AdvertisementMedium } from '../definitions';
import { ActivatedRoute } from '@angular/router';
import { ARTICLE_BODY_DESKTOP_ADS, ARTICLE_BODY_MOBILE_ADS, ARTICLE_BODY_ROADBLOCK_ADS } from '../constants';

export const HEADINGS_v2 = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
export const LISTS_v2 = ['ul', 'ol'];
export const SPECIAL_TAGS_v2 = ['span'];
export const TO_SKIP_TAGS_v2 = [...HEADINGS_v2, ...LISTS_v2, ...SPECIAL_TAGS_v2];
export const ALL_TEXT_TAGS_V2 = ['p', ...TO_SKIP_TAGS_v2];

/**
 * Enhanced version of AutoArticleBodyAdService that places ads every 5 elements
 * (paragraphs, highlighted sections, images) with up to 6 zones for extra long articles.
 * Uses new banner naming convention: box_X, mobilrectangle_X, roadblock_X
 */
@Injectable()
export class AutoArticleBodyAdV2Service {
  structure: ArticleBody[] = [];
  /**
   * Buffer to store iterated wysiwyg element before they are added to a wysiwyg component.
   */
  wysiwygBuffer: Element[] = [];

  /**
   * Constructed body that will contain the original body with the inserted ads.
   */
  constructedBody: ArticleBody[] = [];

  enableDebug = false;

  /**
   * Maximum number of ad zones to insert in article content
   */
  private readonly MAX_AD_ZONES = 6;

  /**
   * Number of elements between ads (paragraphs, highlighted sections, images)
   */
  private readonly ELEMENTS_BETWEEN_ADS = 5;

  constructor(
    @Inject(DOCUMENT) private readonly document: Document,
    @Optional() private activatedRoute: ActivatedRoute
  ) {}

  debug(...args: unknown[]): void {
    if (this.enableDebug) {
      console.warn(args[0], ...args.slice(1));
    }
  }

  init(body: ArticleBody[]): void {
    if (!this.enableDebug && this.activatedRoute?.snapshot?.queryParams['interrupter_debug'] !== undefined) {
      this.enableDebug = true;
    }

    if (!body) {
      return;
    }

    this.structure = this.createStruct(body);

    this.debug('1. Created raw struct: ', this.structure);

    const removeEadverts = this.structure.filter((item) => item?.type !== ArticleBodyType.Advert) as unknown as Element[];

    this.debug('2. Cleaned EAdverts from struct: ', removeEadverts);

    this.structure = this.addAdverts(removeEadverts);
  }

  createStruct(body: ArticleBody[]) {
    return body.reduce((acc, component: ArticleBody) => {
      if (this.isWysiwygComponent(component)) {
        const root = this.document.createElement('div');
        root.innerHTML = component.details[0].value;
        // Not a real-world use-case but we need to handle plain text in the value. Sometimes migrated contents
        // can have plain text values which would display on the portal. We need to wrap these in a p tag to create valid html elements.
        if (component.details[0]?.value.length > 0 && root.childElementCount === 0) {
          root.innerHTML = `<p>${component.details[0]?.value.trim()}</p>`;
        }
        const elements = Array.from(root.children);
        return (acc = [...acc, ...elements] as ArticleBody[]);
      } else {
        return (acc = [...acc, component]);
      }
    }, [] as ArticleBody[]);
  }

  /**
   * Runs the whole automatic ad insertion logic based on the body and config provided in the init() function.
   */
  autoAd(): ArticleBody[] {
    this.constructedBody = [];
    this.structure.map((_item, index) => {
      this.iterate(index);
    });

    this.debug('4. Created body: ', this.constructedBody);

    return this.constructedBody;
  }

  /**
   * Enhanced ad insertion logic that counts elements (paragraphs, highlighted sections, images)
   * and inserts ads every 5 elements, up to 6 zones maximum.
   */
  addAdverts(structure: Element[]): ArticleBody[] {
    const unfilteredWithAdverts: {
      element: ArticleBody | Element;
      elementCount: number;
      type: 'advert' | 'non-content' | 'empty' | 'content';
    }[] = [];

    let elementCount = 0;
    let adZoneCount = 0;

    structure.forEach((element, i) => {
      const isContentElement = this.isContentElement(element);
      const hasText = element?.textContent?.trim()?.length > 0;

      // Count content elements (paragraphs, highlighted sections, images)
      if (isContentElement) {
        elementCount++;
      }

      unfilteredWithAdverts.push({
        element,
        elementCount: elementCount,
        type: !hasText ? 'empty' : !isContentElement ? 'non-content' : 'content',
      });

      // Check if we should insert an ad after this element
      const shouldInsertAd = this.shouldInsertAd(element, structure, i, elementCount, adZoneCount);

      if (shouldInsertAd) {
        adZoneCount++;
        unfilteredWithAdverts.push({
          element: this.createEadvert(adZoneCount),
          elementCount: 0,
          type: 'advert',
        });
      }
    });

    this.debug('3. Added adverts to struct: ', unfilteredWithAdverts);

    return unfilteredWithAdverts.map((item) => item.element) as ArticleBody[];
  }

  /**
   * Determines if an ad should be inserted after the current element
   */
  private shouldInsertAd(element: Element, structure: Element[], index: number, elementCount: number, adZoneCount: number): boolean {
    // Don't exceed maximum ad zones
    if (adZoneCount >= this.MAX_AD_ZONES) {
      return false;
    }

    // Only insert after content elements
    if (!this.isContentElement(element)) {
      return false;
    }

    // Insert every 5 elements
    if (elementCount % this.ELEMENTS_BETWEEN_ADS !== 0) {
      return false;
    }

    // Don't insert if we're too close to the end
    const remainingContentElements = this.countRemainingContentElements(structure, index);
    if (remainingContentElements < 2) {
      return false;
    }

    // Don't insert after headings or special elements
    if (this.isElementInList(element, TO_SKIP_TAGS_v2)) {
      return false;
    }

    // Check if next element is suitable for ad placement
    const nextElement = structure[index + 1];
    if (nextElement && this.isElementInList(nextElement, [...LISTS_v2, ...SPECIAL_TAGS_v2])) {
      return false;
    }

    return true;
  }

  /**
   * Counts remaining content elements from current position
   */
  private countRemainingContentElements(structure: Element[], fromIndex: number): number {
    return structure.slice(fromIndex + 1).filter((element) => this.isContentElement(element)).length;
  }

  /**
   * Determines if an element is a content element (paragraph, highlighted section, image)
   */
  private isContentElement(element: Element): boolean {
    if (!element) return false;

    const tagName = this.getTagName(element);
    const hasText = element?.textContent?.trim()?.length > 0;

    // Paragraphs with text
    if (tagName === 'p' && hasText) {
      return true;
    }

    // Images (figure elements with image class)
    if (tagName === 'figure' && element.className?.includes('image')) {
      return true;
    }

    // Highlighted sections (spans with content)
    if (tagName === 'span' && hasText) {
      return true;
    }

    return false;
  }

  isWysiwygComponent(component: ArticleBody): boolean {
    return component?.type === ArticleBodyType.Wysywyg;
  }

  getTagName(element: Element): string {
    return element?.tagName?.toLowerCase() || '';
  }

  isElementInList(element: Element, list: string[]): boolean {
    return list.includes(this.getTagName(element));
  }

  /**
   * Determines whether the given element should be treated as text element. This checks based on the provided config.
   */
  isTextContent(item: Element): boolean {
    return item instanceof Element && ALL_TEXT_TAGS_V2.includes(item.tagName?.toLowerCase());
  }

  getIteratorParams(structIndex: number): Record<'next' | 'current', ArticleBody | null> {
    const next = structIndex - 1 < this.structure.length ? this.structure[structIndex + 1] : null;
    const current = this.structure[structIndex] as ArticleBody;
    return {
      next,
      current,
    };
  }

  /**
   * Called in each iteration.
   * This function checks whether the current element should be treated as a paragraph and increments the paragraph counter.
   * It also handles to push the components to the new body based on their type.
   */
  iterate(structIndex: number): void {
    const { next, current } = this.getIteratorParams(structIndex);

    if (current instanceof Element) {
      this.wysiwygBuffer.push(current);
      if (!(next instanceof Element)) {
        this.flushWysiwygBuffer();
      }
    } else {
      this.constructedBody.push(current as ArticleBody);
    }
  }

  /**
   * Flushes the wysiwyg buffer.
   * The wysiwyg buffer is used to store elements that should be then used in a wysiwyg component.
   * This function flushes the buffer and the items that are
   * in the buffer then used to create a wysiwyg component that is inserted to the new body.
   */
  flushWysiwygBuffer(): void {
    if (this.wysiwygBuffer.length === 0) {
      return;
    }

    const wysiwygComponent = this.createWysiwygComponent(this.wysiwygBuffer);
    this.wysiwygBuffer = [];
    this.constructedBody.push(wysiwygComponent);
  }

  /**
   * Creates a WYSIWYG component based on the DOM elements that are passed into the input.
   * @param children elements that should be inserted into the WYSIWYG component
   */
  createWysiwygComponent(children: Element[]): ArticleBody {
    const root = this.document.createElement('div');
    children.map((node) => root.appendChild(node));
    const id = Math.floor(Math.random() * 10000);
    return {
      id: 'auto_wysiwyg_' + id,
      type: ArticleBodyType.Wysywyg,
      subComponents: [],
      details: [
        {
          key: 'text',
          type: ArticleBodyDetailType.WysywygDetail,
          value: root.innerHTML,
        },
      ],
    };
  }

  /**
   * Gets the appropriate banner name for a given zone and medium
   * @param zoneNumber The zone number (1-6)
   * @param medium The advertisement medium
   */
  getBannerNameForZone(zoneNumber: number, medium: AdvertisementMedium): AdvertisementBannerName {
    const zoneIndex = Math.min(Math.max(zoneNumber - 1, 0), 5); // Ensure index is 0-5

    switch (medium) {
      case 'desktop':
        return ARTICLE_BODY_DESKTOP_ADS[zoneIndex];
      case 'mobile':
        return ARTICLE_BODY_MOBILE_ADS[zoneIndex];
      default:
        return ARTICLE_BODY_ROADBLOCK_ADS[zoneIndex];
    }
  }

  /**
   * Gets all banner names for article body ads for a specific medium
   * @param medium The advertisement medium
   */
  getArticleBodyBannerNames(medium: AdvertisementMedium): AdvertisementBannerName[] {
    switch (medium) {
      case 'desktop':
        return ARTICLE_BODY_DESKTOP_ADS;
      case 'mobile':
        return ARTICLE_BODY_MOBILE_ADS;
      default:
        return ARTICLE_BODY_ROADBLOCK_ADS;
    }
  }

  /**
   * Creates an Eadvert component with zone-specific DIV ID.
   * Creates DIV with ID pattern: Mandiner_normal_content_X
   * @param zoneNumber The zone number (1-6)
   */
  createEadvert(zoneNumber: number = 1): ArticleBody {
    const id = Math.floor(Math.random() * 10000);
    const divId = `Mandiner_normal_content_${zoneNumber}`;

    return {
      id: `auto_ad_zone_${zoneNumber}_${id}`,
      type: ArticleBodyType.Advert,
      subComponents: [],
      details: [
        {
          type: ArticleBodyDetailType.AdvertDetail,
          key: 'text',
          value: `<div id="${divId}"></div>`,
        },
      ],
    };
  }

  /**
   * Creates an Eadvert component with specific banner name for a medium
   * Creates DIV with ID pattern: Mandiner_normal_content_X
   * @param zoneNumber The zone number (1-6)
   * @param medium The advertisement medium
   */
  createEadvertForMedium(zoneNumber: number, medium: AdvertisementMedium): ArticleBody {
    const bannerName = this.getBannerNameForZone(zoneNumber, medium);
    const id = Math.floor(Math.random() * 10000);
    const divId = `Mandiner_normal_content_${zoneNumber}`;

    return {
      id: `auto_ad_${bannerName}_${id}`,
      type: ArticleBodyType.Advert,
      subComponents: [],
      details: [
        {
          type: ArticleBodyDetailType.AdvertDetail,
          key: 'text',
          value: `<div id="${divId}"></div>`,
        },
        {
          type: ArticleBodyDetailType.AdvertDetail,
          key: 'bannerName',
          value: bannerName,
        },
        {
          type: ArticleBodyDetailType.AdvertDetail,
          key: 'medium',
          value: medium,
        },
        {
          type: ArticleBodyDetailType.AdvertDetail,
          key: 'zoneNumber',
          value: zoneNumber.toString(),
        },
      ],
    };
  }
}
