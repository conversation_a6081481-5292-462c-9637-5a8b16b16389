import { TestBed } from '@angular/core/testing';
import { DOCUMENT } from '@angular/common';
import { AutoArticleBodyAdV2Service } from './auto-article-body-ad-v2.service';
import { ArticleBody, ArticleBodyType, ArticleBodyDetailType } from '../definitions';

describe('AutoArticleBodyAdV2Service', () => {
  let service: AutoArticleBodyAdV2Service;
  let mockDocument: Document;

  beforeEach(() => {
    mockDocument = {
      createElement: jasmine.createSpy('createElement').and.callFake((tagName: string) => {
        const element = document.createElement(tagName);
        return element;
      })
    } as any;

    TestBed.configureTestingModule({
      providers: [
        AutoArticleBodyAdV2Service,
        { provide: DOCUMENT, useValue: mockDocument }
      ]
    });
    service = TestBed.inject(AutoArticleBodyAdV2Service);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });



  describe('isContentElement', () => {
    it('should identify paragraphs with text as content elements', () => {
      const p = document.createElement('p');
      p.textContent = 'This is a paragraph with content';
      expect(service['isContentElement'](p)).toBe(true);
    });

    it('should identify images as content elements', () => {
      const figure = document.createElement('figure');
      figure.className = 'image';
      expect(service['isContentElement'](figure)).toBe(true);
    });

    it('should identify spans with text as content elements', () => {
      const span = document.createElement('span');
      span.textContent = 'Highlighted text';
      expect(service['isContentElement'](span)).toBe(true);
    });

    it('should not identify empty paragraphs as content elements', () => {
      const p = document.createElement('p');
      expect(service['isContentElement'](p)).toBe(false);
    });

    it('should not identify headings as content elements', () => {
      const h1 = document.createElement('h1');
      h1.textContent = 'Heading';
      expect(service['isContentElement'](h1)).toBe(false);
    });
  });



  describe('createEadvert', () => {
    it('should create advert with correct DIV ID', () => {
      const advert = service.createEadvert(3);

      expect(advert.type).toBe(ArticleBodyType.Advert);
      expect(advert.id).toContain('auto_ad_zone_3');

      const textDetail = advert.details.find(d => d.key === 'text');
      expect(textDetail?.value).toBe('<div id="Mandiner_normal_content_3"></div>');
    });
  });

  describe('shouldInsertAd', () => {
    it('should not insert ad if max zones reached', () => {
      const element = document.createElement('p');
      element.textContent = 'Content';
      const structure: Element[] = [element];

      const result = service['shouldInsertAd'](element, structure, 0, 5, 6);
      expect(result).toBe(false);
    });

    it('should not insert ad for non-content elements', () => {
      const element = document.createElement('h1');
      element.textContent = 'Heading';
      const structure: Element[] = [element];

      const result = service['shouldInsertAd'](element, structure, 0, 5, 0);
      expect(result).toBe(false);
    });

    it('should not insert ad if not at 5-element interval', () => {
      const element = document.createElement('p');
      element.textContent = 'Content';
      const structure: Element[] = [element];

      const result = service['shouldInsertAd'](element, structure, 0, 3, 0);
      expect(result).toBe(false);
    });
  });

  describe('integration test with sample article body', () => {
    it('should insert ads at correct intervals', () => {
      const sampleBody: ArticleBody[] = [
        {
          id: '1',
          type: ArticleBodyType.Wysywyg,
          subComponents: [],
          details: [{
            key: 'text',
            type: ArticleBodyDetailType.WysywygDetail,
            value: '<p>Paragraph 1</p><p>Paragraph 2</p><p>Paragraph 3</p><p>Paragraph 4</p><p>Paragraph 5</p><p>Paragraph 6</p><p>Paragraph 7</p><p>Paragraph 8</p><p>Paragraph 9</p><p>Paragraph 10</p><p>Paragraph 11</p><p>Paragraph 12</p>'
          }]
        }
      ];

      service.init(sampleBody);
      const result = service.autoAd();

      // Should have original content plus ads
      expect(result.length).toBeGreaterThan(1);

      // Count ads in result
      const ads = result.filter(item => item.type === ArticleBodyType.Advert);
      expect(ads.length).toBeGreaterThan(0);
      expect(ads.length).toBeLessThanOrEqual(6); // Max 6 zones
    });
  });
});
