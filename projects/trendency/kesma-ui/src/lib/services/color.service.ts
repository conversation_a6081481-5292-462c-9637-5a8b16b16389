import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ColorService {
  hexToRgb(color: string): string | null {
    color = color.trim().toLowerCase();

    if (color.startsWith('#')) {
      let hex = color.slice(1);

      if (hex.length === 3) {
        hex = hex
          .split('')
          .map((c) => c + c)
          .join('');
      }

      if (hex.length !== 6) {
        return null;
      }

      const r = parseInt(hex.slice(0, 2), 16);
      const g = parseInt(hex.slice(2, 4), 16);
      const b = parseInt(hex.slice(4, 6), 16);

      return `rgb(${r}, ${g}, ${b})`;
    }

    if (color.startsWith('rgb')) {
      return color;
    }

    return null;
  }

  colorIsLight(color: string, brightnessLimit = 186): boolean {
    const match = color.match(/\d+/g);
    const [r, g, b] = match!.map(Number);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > brightnessLimit;
  }
}
