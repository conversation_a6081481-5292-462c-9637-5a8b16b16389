import { inject, Injectable } from '@angular/core';
import { injectEnvironment, RESPONSE, UtilService } from '@trendency/kesma-core';

@Injectable({
  providedIn: 'root',
})
export class RedirectService {
  private readonly response = inject(RESPONSE, { optional: true });
  private readonly utilService = inject(UtilService);
  private readonly environment = injectEnvironment();

  /**
   * Redirect location to the given URL.
   *
   * @param url The URL to redirect to.
   * @param skipSiteUrl If true, the site URL will NOT be prepended to the URL.
   * @param code Status code of the redirect.
   */
  redirectOldUrl(url: string, skipSiteUrl = false, code = 301): void {
    const redirectUrl = skipSiteUrl ? url : `${this.environment.siteUrl}/${url}`;

    if (this.utilService.isBrowser()) {
      location.href = redirectUrl;
      return;
    }

    this.response?.status(code);
    this.response?.setHeader('location', redirectUrl);
  }

  /**
   * Determines whether a redirect should occur based on the current page number
   * and the presence of data.
   *
   * @param currentPage The current page number.
   * @param data An array of data items. The function checks if this array is non-empty to determine if there is relevant data to display.
   * @returns A boolean indicating whether a redirect should occur.
   */
  shouldBeRedirect<T>(currentPage: number, data: T[]): boolean {
    return !!currentPage && !data?.length;
  }
}
