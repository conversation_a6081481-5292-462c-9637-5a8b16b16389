import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ConvertEmbededLinkService {
  getEmdededLink(url: string): string {
    if (url.includes('videa')) {
      return this.getVideaUrl(url);
    }
    if (url.includes('youtu.be') || url.includes('youtube.com')) {
      return this.getEmbededYouTubeLink(url);
    }
    return url;
  }

  private getVideaUrl(url: string): string {
    let videaUrl = '';
    if (url.startsWith('//videa.hu/player?v=')) {
      videaUrl = url;
    } else {
      const urlSplit = url.split('-');
      videaUrl = `//videa.hu/player?v=${urlSplit[urlSplit.length - 1]}`;
    }
    return `${videaUrl}`;
  }

  /**
   * Átalakítja a YouTube videó linket beágyazható formátumra
   * @param link - A YouTube videó link
   * @returns A beágyazható YouTube link
   */
  private getEmbededYouTubeLink = (url: string): string => {
    if (!url) {
      return '';
    }
    if (url.includes('/shorts/')) {
      return url.replace('/shorts/', '/embed/');
    }

    // watch link: https://www.youtube.com/watch?v=abc123
    if (url.includes('watch?v=')) {
      const videoId = new URL(url).searchParams.get('v');
      return `https://www.youtube.com/embed/${videoId}`;
    }
    return url;
  };
}
