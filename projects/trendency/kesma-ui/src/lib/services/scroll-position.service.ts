import { DestroyRef, inject, Injectable } from '@angular/core';
import { NavigationStart, Router, Scroll } from '@angular/router';
import { asyncScheduler, finalize, Observable, of, tap, timer } from 'rxjs';
import { filter, observeOn, switchMap, takeUntil } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { UtilService } from '@trendency/kesma-core';

@Injectable({
  providedIn: 'root',
})
export class ScrollPositionService {
  private readonly utilService = inject(UtilService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly router = inject(Router);
  private lastSavedYPosition = 0;

  setupScrollPositionListener(): void {
    if (!this.utilService.isBrowser()) {
      return;
    }

    const html = document.querySelector('html')!;
    const windowHeight = window.innerHeight;
    const navStart$ = this.router.events.pipe(
      filter((e) => e instanceof NavigationStart),
      tap(() => (this.lastSavedYPosition = 0))
    ) as Observable<NavigationStart>;

    (this.router.events.pipe(filter((e) => e instanceof Scroll)) as Observable<Scroll>)
      .pipe(
        filter((event: Scroll) => !!event.position),
        observeOn(asyncScheduler),
        switchMap((event: Scroll) => {
          const hasPos = event.position;
          if (!hasPos) {
            return of(null);
          }
          html.style.minHeight = `${hasPos[1] + windowHeight}px`;
          window.scrollTo(...hasPos);
          this.lastSavedYPosition = hasPos[1];
          return timer(10_000).pipe(
            finalize(() => (html.style.minHeight = '')),
            takeUntil(navStart$)
          );
        }),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe();
  }

  restoreScrollPosition(): void {
    if (!this.utilService.isBrowser()) {
      return;
    }
    setTimeout(() => window.scrollTo(0, this.lastSavedYPosition), 10);
  }
}
