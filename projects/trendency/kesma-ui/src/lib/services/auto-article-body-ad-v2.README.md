# AutoArticleBodyAdV2Service

Enhanced version of the automatic article body advertisement service that implements the new advertisement placement strategy based on element counting rather than character thresholds.

## Key Features

### 1. Element-Based Placement
- Places ads every **5 elements** (paragraphs, highlighted sections, images)
- Maximum of **6 ad zones** per article to handle extra long content
- More predictable and consistent ad placement

### 2. Fixed DIV Structure
Creates advertisement DIVs with specific IDs following the pattern:
```html
<div id="Mandiner_normal_content_1"></div>
<div id="Mandiner_normal_content_2"></div>
<div id="Mandiner_normal_content_3"></div>
<div id="Mandiner_normal_content_4"></div>
<div id="Mandiner_normal_content_5"></div>
<div id="Mandiner_normal_content_6"></div>
```

Also supports banner naming convention for different mediums:
- **Desktop**: `box_1`, `box_2`, `box_3`, `box_4`, `box_5`, `box_6`
- **Mobile**: `mobilrectangle_1`, `mobilrectangle_2`, `mobilrectangle_3`, `mobilrectangle_4`, `mobilrectangle_5`, `mobilrectangle_6`
- **Roadblock**: `roadblock_1`, `roadblock_2`, `roadblock_3`, `roadblock_4`, `roadblock_5`, `roadblock_6`

### 3. Content Element Recognition
The service recognizes these as content elements for counting:
- **Paragraphs** (`<p>`) with text content
- **Images** (`<figure>` elements with `image` class)
- **Highlighted sections** (`<span>` elements with text content)

## Usage

### Basic Usage
```typescript
import { AutoArticleBodyAdV2Service } from '@trendency/kesma-ui';

@Component({
  providers: [AutoArticleBodyAdV2Service]
})
export class ArticleComponent {
  constructor(private adService: AutoArticleBodyAdV2Service) {}

  processArticleBody(body: ArticleBody[]) {
    this.adService.init(body);
    return this.adService.autoAd();
  }
}
```

### Getting Banner Names for Specific Medium
```typescript
// Get all desktop banner names for article body
const desktopBanners = this.adService.getArticleBodyBannerNames('desktop');
// Returns: ['box_1', 'box_2', 'box_3', 'box_4', 'box_5', 'box_6']

// Get specific banner name for zone 3 on mobile
const mobileBanner = this.adService.getBannerNameForZone(3, 'mobile');
// Returns: 'mobilrectangle_3'
```

### Creating Ads for Specific Medium
```typescript
// Create an ad for zone 2 on desktop
const desktopAd = this.adService.createEadvertForMedium(2, 'desktop');
// Creates: <div id="Mandiner_normal_content_2"></div> with banner name 'box_2'

// Create an ad for zone 4 on mobile
const mobileAd = this.adService.createEadvertForMedium(4, 'mobile');
// Creates: <div id="Mandiner_normal_content_4"></div> with banner name 'mobilrectangle_4'

// Create a basic ad for zone 3
const basicAd = this.adService.createEadvert(3);
// Creates: <div id="Mandiner_normal_content_3"></div>
```

## Algorithm Details

### Ad Placement Logic
1. **Element Counting**: Counts paragraphs, images, and highlighted sections
2. **5-Element Intervals**: Places ads after every 5th content element
3. **Zone Limit**: Maximum 6 ad zones per article
4. **Smart Positioning**: Avoids placing ads:
   - After headings or special elements
   - Too close to the end of the article
   - Before lists or special formatting

### Example Placement
For an article with 15 content elements:
- **Zone 1**: After element 5
- **Zone 2**: After element 10
- **Zone 3**: After element 15 (if sufficient content remains)

## Differences from Original Service

| Feature | Original Service | V2 Service |
|---------|------------------|------------|
| Placement Logic | Character count (2000, 4000, 6000, 8000, 10000) | Element count (every 5 elements) |
| Max Ads | 5 zones | 6 zones |
| Banner Names | Various legacy names | Standardized: box_X, mobilrectangle_X, roadblock_X |
| Content Recognition | Text-based | Element-based (paragraphs, images, highlights) |
| Predictability | Variable based on text length | Consistent based on element count |

## Configuration Constants

The service uses these constants from `advertisement.constants.ts`:

```typescript
export const ARTICLE_BODY_DESKTOP_ADS: AdvertisementBannerName[] = [
  'box_1', 'box_2', 'box_3', 'box_4', 'box_5', 'box_6'
];

export const ARTICLE_BODY_MOBILE_ADS: AdvertisementBannerName[] = [
  'mobilrectangle_1', 'mobilrectangle_2', 'mobilrectangle_3', 
  'mobilrectangle_4', 'mobilrectangle_5', 'mobilrectangle_6'
];

export const ARTICLE_BODY_ROADBLOCK_ADS: AdvertisementBannerName[] = [
  'roadblock_1', 'roadblock_2', 'roadblock_3', 
  'roadblock_4', 'roadblock_5', 'roadblock_6'
];
```

## Debug Mode

Enable debug mode by adding `?interrupter_debug` to the URL query parameters. This will log:
- Raw structure creation
- Ad insertion points
- Final constructed body

## Testing

The service includes comprehensive unit tests covering:
- Banner name generation for different mediums
- Content element recognition
- Ad placement logic
- Integration tests with sample article bodies

Run tests with:
```bash
ng test --include="**/auto-article-body-ad-v2.service.spec.ts"
```

## Migration from Original Service

To migrate from the original `AutoArticleBodyAdService`:

1. Replace the service injection:
```typescript
// Old
constructor(private adService: AutoArticleBodyAdService) {}

// New
constructor(private adService: AutoArticleBodyAdV2Service) {}
```

2. Update imports:
```typescript
import { AutoArticleBodyAdV2Service } from '@trendency/kesma-ui';
```

3. The API remains the same (`init()` and `autoAd()` methods), but the placement logic and banner names will change according to the new specification.

## Production Deployment Notes

- Test on articles of varying lengths (short, normal, long)
- Verify ad placement on older published articles
- Ensure sufficient article length to display all 6 zones when needed
- Monitor ad performance with the new consistent placement strategy
