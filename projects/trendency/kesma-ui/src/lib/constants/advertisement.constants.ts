import { AdvertisementBannerName } from '../definitions';

export const TECHNICAL_ADS: AdvertisementBannerName[] = ['technikai_1', 'technikai_2', 'technikai_3', 'technikai_4', 'technikai_5'];
export const ECOMM_ADS: AdvertisementBannerName[] = ['ecomm_1', 'ecomm_2', 'ecomm_3', 'ecomm_4', 'ecomm_5', 'ecomm_6'];
export const GOOGLE_NATIV_ADS: AdvertisementBannerName[] = ['googlenativ_1', 'googlenativ_2', 'googlenativ_3'];
export const MOBILE_INTERRUPTER_ADS: AdvertisementBannerName[] = [
  'mobilinterrupter_1',
  'mobilinterrupter_2',
  'mobilinterrupter_3',
  'mobilinterrupter_4',
  'mobilinterrupter_5',
];
export const DESKTOP_INTERRUPTER_ADS: AdvertisementBannerName[] = [
  'desktopinterrupter_1',
  'desktopinterrupter_2',
  'desktopinterrupter_3',
  'desktopinterrupter_4',
  'desktopinterrupter_5',
];

export const PR_CIKK_FIX: AdvertisementBannerName[] = ['prcikkfix_1', 'prcikkfix_2', 'prcikkfix_3', 'prcikkfix_4', 'prcikkfix_5'];
export const KEPES_AJANLO: AdvertisementBannerName[] = ['kepes-ajanlo_1', 'kepes-ajanlo_2', 'kepes-ajanlo_3'];

// Article body ad zones - using new naming convention
export const ARTICLE_BODY_DESKTOP_ADS: AdvertisementBannerName[] = [
  'box_1', 'box_2', 'box_3', 'box_4', 'box_5', 'box_6'
];
export const ARTICLE_BODY_MOBILE_ADS: AdvertisementBannerName[] = [
  'mobilrectangle_1', 'mobilrectangle_2', 'mobilrectangle_3', 'mobilrectangle_4', 'mobilrectangle_5', 'mobilrectangle_6'
];
export const ARTICLE_BODY_ROADBLOCK_ADS: AdvertisementBannerName[] = [
  'roadblock_1', 'roadblock_2', 'roadblock_3', 'roadblock_4', 'roadblock_5', 'roadblock_6'
];

export const ALL_BANNER_LIST: AdvertisementBannerName[] = [
  'interstitial',
  'layer',
  'leaderboard_1',
  'leaderboard_2',
  'leaderboard_3',
  'leaderboard_4',
  'leaderboard_5',
  'leaderboard_6',
  'leaderboard_7',
  'leaderboard_8',
  'leaderboard_9',
  'leaderboard_10',
  'box_1',
  'box_2',
  'box_3',
  'box_4',
  'box_5',
  'box_6',
  'box_7',
  'box_8',
  'box_9',
  'box_10',
  'roadblock_1',
  'roadblock_2',
  'roadblock_3',
  'roadblock_4',
  'roadblock_5',
  'roadblock_6',
  'roadblock_7',
  'roadblock_8',
  'roadblock_9',
  'roadblock_10',
  'mobilrectangle_1',
  'mobilrectangle_2',
  'mobilrectangle_3',
  'mobilrectangle_4',
  'mobilrectangle_5',
  'mobilrectangle_6',
  'mobilrectangle_7',
  'mobilrectangle_8',
  'mobilrectangle_9',
  'mobilrectangle_10',
  'pr_box',
  'billboard',
  'footer_ad',
  'games_page',
  'mobilbottom',
  'mobiltop',
  'szponzorcsik',
  'mobilrectangle_footer',
  'leaderboard_footer',
  'recommendation_1',
  'roadblock_ottboxextra',
  'mobilrectangle_ottboxextra',
  'szponzorcsik_also',
  'szponzorcsik_felso',
  ...KEPES_AJANLO,
  ...TECHNICAL_ADS,
  ...ECOMM_ADS,
  ...GOOGLE_NATIV_ADS,
  ...MOBILE_INTERRUPTER_ADS,
  ...DESKTOP_INTERRUPTER_ADS,
  ...PR_CIKK_FIX,
];

export const PAGE_TYPES = {
  all_articles_and_sub_pages: 'all_articles_and_sub_pages',
  main_page: 'main_page',
  column_sport_all_articles_and_sub_pages: 'column_sport_all_articles_and_sub_pages',
  other_pages: 'other_pages',
  recipe_pages: 'recipe_pages',
  article_pages: 'article_pages',
  search_page: 'search_page',
  search: 'search',
};

export const ADVERT_CONTENT_COLUMN_TYPES = [
  {
    value: 'main_page',
    label: 'Főoldal',
  },
  {
    value: 'search_page',
    label: 'Keresés aloldal',
  },
  {
    value: 'search',
    label: 'Keresés',
  },
  {
    value: 'opinion',
    label: 'Vélemény',
  },
  {
    value: 'all_articles_and_sub_pages',
    label: 'Összes Cikk és Aloldal',
  },
  {
    value: 'custom_built_page',
    label: 'Egyedi Épített oldal',
  },
  {
    value: 'other_pages',
    label: 'Egyéb aloldal',
  },
  {
    value: 'recipe_pages',
    label: 'Recept aloldal',
  },
  {
    value: 'article_pages',
    label: 'Cikkoldal',
  },
  {
    value: 'gallery',
    label: 'Galéria',
  },
  {
    value: 'sport',
    label: 'Sport',
  },
  {
    value: 'grief',
    label: 'Gyász',
  },
  {
    value: 'pr',
    label: 'PR',
  },
  {
    value: 'everywhere',
    label: 'Teljes oldal',
  },
  {
    value: 'aprohirdetes',
    label: 'Apróhirdetés aloldal',
  },
  {
    value: 'column_sport_all_articles_and_sub_pages',
    label: 'Sport aloldal',
  },
  {
    value: 'allas',
    label: 'Állás aloldal',
  },
  {
    value: 'allasok',
    label: 'Állások aloldal',
  },
];
