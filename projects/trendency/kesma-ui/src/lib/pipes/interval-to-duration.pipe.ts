import { Pipe, PipeTransform } from '@angular/core';
import { Duration, intervalToDuration } from 'date-fns';

@Pipe({
  name: 'intervalToDuration',
})
export class IntervalToDurationPipe implements PipeTransform {
  transform(interval: number | null): string {
    const duration: Duration = intervalToDuration({ start: 0, end: (interval || 0) * 1000 });

    const zeroPad = (num: number) => String(num).padStart(2, '0');

    return `${duration?.minutes || 0}:${zeroPad(duration?.seconds || 0)}`;
  }
}
