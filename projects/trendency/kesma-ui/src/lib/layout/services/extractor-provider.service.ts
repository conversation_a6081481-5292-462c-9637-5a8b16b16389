import { Inject, Injectable, Optional } from '@angular/core';
import {
  DATA_EXTRACTOR_CONFIGS,
  DATA_EXTRACTORS,
  DATA_ITERATOR_EXTRACTORS,
  DataExtractor,
  DataExtractorDef,
  DataExtractorIterableDef,
  DataExtractorRepository,
  DataExtractorRepositoryItem,
  LayoutDataExtractorService,
  LayoutDataIterableExtractorService,
  PORTAL_DATA_EXTRACTOR_CONFIGS,
  PORTAL_DATA_EXTRACTORS,
  PORTAL_DATA_ITERATOR_EXTRACTORS,
} from '../definitions';
import { LayoutElementContent, LayoutElementContentType } from '../../definitions';
import { injectEnvironmentWithOptions } from '@trendency/kesma-core';

@Injectable()
export class ExtractorProviderService<R = unknown> {
  private readonly environment = injectEnvironmentWithOptions({ optional: true });
  constructor(
    @Optional() @Inject(PORTAL_DATA_EXTRACTORS) protected readonly portalExtractors: LayoutDataExtractorService<R>[],
    @Optional() @Inject(DATA_EXTRACTORS) protected readonly extractors: LayoutDataExtractorService<R>[],
    @Optional()
    @Inject(PORTAL_DATA_ITERATOR_EXTRACTORS)
    protected readonly portalIteratorExtractors: LayoutDataIterableExtractorService<R>[],
    @Optional() @Inject(DATA_ITERATOR_EXTRACTORS) protected readonly iteratorExtractors: LayoutDataIterableExtractorService<R>[],
    @Optional() @Inject(PORTAL_DATA_EXTRACTOR_CONFIGS) protected readonly portalConfig: DataExtractor<any>[],
    @Inject(DATA_EXTRACTOR_CONFIGS) protected readonly config: DataExtractor<any>[]
  ) {
    this.createExtractorRepository();
  }

  extractorRepository: DataExtractorRepository = {};

  getExtractorForLayoutElement(layoutElement: LayoutElementContent): unknown {
    const contentType = layoutElement.contentType;
    if (!contentType) {
      return;
    }
    return this.extract(layoutElement);
  }

  extract(layoutElement: LayoutElementContent): any {
    try {
      const extractorConfig = this.getApplicableExtractor(layoutElement);
      if (extractorConfig && 'isIterable' in extractorConfig) {
        const iteratorExtractor = extractorConfig.extractorInstance as unknown as LayoutDataIterableExtractorService<any>;
        const iterator = iteratorExtractor?.getIterator(layoutElement) || [];
        return iterator.map((i) => {
          const extractedResult = iteratorExtractor.extractIteratorData(layoutElement, i);
          return extractedResult;
        });
      }
      let extractedResult = extractorConfig?.extractorInstance?.extractData(layoutElement);
      if (extractedResult && !extractedResult.meta?.extractedBy) {
        if (!extractedResult.meta) {
          extractedResult.meta = {
            extractedBy: extractorConfig?.extractorInstance?.constructor?.name || 'INVALID',
          };
        }
        if (!extractedResult.meta.extractedBy) {
          extractedResult.meta.extractedBy = extractorConfig?.extractorInstance?.constructor?.name || 'INVALID';
        }
      }
      return extractedResult;
    } catch (e) {
      console.error('Error happened while extracting data: ', { layoutElement, exception: e });
      return {
        data: null,
        meta: {},
      };
    }
  }

  protected getApplicableExtractor(layoutElement: LayoutElementContent): DataExtractorRepositoryItem<unknown> | undefined {
    const contentType = layoutElement.contentType;
    if (!contentType) {
      return;
    }
    const extractorsForContentType = this.extractorRepository[contentType];
    const extractor = extractorsForContentType?.find((extractorDef) => {
      if (extractorDef && 'checkApplicability' in extractorDef && extractorDef.checkApplicability) {
        if (!extractorDef.checkApplicability(layoutElement)) {
          return false;
        }
      }
      return true;
    });
    if (!extractor && this.environment?.production !== true) {
      //console.warn('No applicable extractor found for ' + layoutElement.id, layoutElement);
    }
    return extractor;
  }

  protected createExtractorRepository(): void {
    [...(this.config ?? []), ...(this.portalConfig ?? [])].map((def) => {
      def.supportedContentTypes.map((supportedContentType: LayoutElementContentType) => {
        this.addExtractorForContentType(supportedContentType, def);
      });
    });
    this.normalizeExtractorsForContentTypes();
  }

  protected addExtractorForContentType(contentType: LayoutElementContentType, extractorDefinition: DataExtractor<R>): void {
    const extractorInstance = this.resolveExtractorInstance(extractorDefinition);
    if (!extractorInstance) {
      console.error('ExtractorProviderService > Unable to resolve extractor instance for the following definition: ', extractorDefinition);
      return;
    }
    if (this.extractorRepository && contentType in this.extractorRepository) {
      this.extractorRepository[contentType]!.push({ ...extractorDefinition, extractorInstance });
    } else {
      this.extractorRepository[contentType] = [{ ...extractorDefinition, extractorInstance }];
    }
  }

  protected resolveExtractorInstance(extractorDefinition: DataExtractorDef<R>): LayoutDataExtractorService<R> | undefined {
    if ((extractorDefinition as DataExtractorIterableDef<R>)?.isIterable) {
      return [...(this.iteratorExtractors ?? []), ...(this.portalIteratorExtractors ?? [])]?.find(
        (extractor) => extractor instanceof (extractorDefinition as DataExtractorIterableDef<R>).extractor
      );
    }
    return (
      [...(this.extractors ?? []), ...(this.portalExtractors ?? [])].flat().find((extractor) => extractor instanceof extractorDefinition.extractor) ||
      this.extractors?.find((extractor) => extractor instanceof extractorDefinition.extractor)
    );
  }

  protected normalizeExtractorsForContentTypes(): void {
    Object.keys(this.extractorRepository).map((contentType: string) => {
      const extractorsForContentType = this.extractorRepository[contentType as LayoutElementContentType];
      extractorsForContentType!.sort((a: DataExtractorRepositoryItem<unknown>, b: DataExtractorRepositoryItem<unknown>) => {
        const priorityA = a?.priority || 0;
        const priorityB = b?.priority || 0;
        if (priorityA > priorityB) {
          return -1;
        }
        if (priorityB > priorityA) {
          return 1;
        }

        //Fallback prioritize based on how specific is the given extractor. The less content type is the more advanced the extractor is.
        if (a.supportedContentTypes.length > b.supportedContentTypes.length) {
          return -1;
        }
        if (b.supportedContentTypes.length > a.supportedContentTypes.length) {
          return 1;
        }
        return 0;
      });
    });
  }
}
