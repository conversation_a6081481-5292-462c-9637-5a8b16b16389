import { Injectable } from '@angular/core';
import { BlockTitleWithTags, LayoutElementContent, LayoutElementContentConfigurationBlockTitleWithTags } from '../../definitions';

import { DataExtractorFunction, LayoutDataExtractorService } from '../definitions';

@Injectable()
export class BlockTitleWithTagsExtractor implements LayoutDataExtractorService<BlockTitleWithTags | undefined> {
  extractData: DataExtractorFunction<BlockTitleWithTags | undefined> = (element: LayoutElementContent) => {
    const conf = element.config as LayoutElementContentConfigurationBlockTitleWithTags;
    if (!conf) {
      return;
    }

    return {
      data: {
        selectedTags: conf.selectedTags,
        title: conf.title,
      },
    };
  };
}
