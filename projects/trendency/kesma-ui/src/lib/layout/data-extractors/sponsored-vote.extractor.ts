import { inject, Injectable } from '@angular/core';
import { DataExtractorFunction, LayoutDataExtractorService } from '../definitions';
import {
  LayoutElementContent,
  LayoutElementContentConfigurationSponsoredVote,
  LayoutElementContentConfigurationVote,
  SponsoredVoteDataWithAnswer,
} from '../../definitions';
import { backendVotingDataToVotingData } from '../../utils';
import { VoteService } from '../../services';

@Injectable()
export class SponsoredVoteExtractor implements LayoutDataExtractorService<SponsoredVoteDataWithAnswer | undefined> {
  private readonly voteService = inject(VoteService);

  extractData: DataExtractorFunction<SponsoredVoteDataWithAnswer | undefined> = (element: LayoutElementContent) => {
    const conf = element.config as LayoutElementContentConfigurationVote;

    if (!conf || !conf?.selectedVote?.data) {
      return;
    }

    const voteData = backendVotingDataToVotingData(conf?.selectedVote?.data);
    const data = this.voteService.getVoteData(voteData);
    const sponsoredVoteElementConfig = element.config as LayoutElementContentConfigurationSponsoredVote;

    return {
      data: {
        ...data,
        isAdOceanEnabled: sponsoredVoteElementConfig?.isAdOceanEnabled,
        adOceanId: sponsoredVoteElementConfig?.adOceanId,
      },
      meta: {
        extractedBy: SponsoredVoteExtractor.name,
      },
    };
  };
}
