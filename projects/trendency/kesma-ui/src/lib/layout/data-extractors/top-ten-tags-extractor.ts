import { Injectable } from '@angular/core';
import { LayoutElementContent, LayoutElementContentConfigurationTopTenTags } from '../../definitions';

import { DataExtractorFunction, LayoutDataExtractorService } from '../definitions';

interface Tag {
  tags: SelectedTags[];
}

interface SelectedTags {
  coverThumbnailUrl: string;
  generatedDynamically: number;
  id: string;
  obtainedDynamically: false;
  slug: string;
  title: string;
}

@Injectable()
export class TopTenTagsExtractor implements LayoutDataExtractorService<Tag | undefined> {
  extractData: DataExtractorFunction<Tag | undefined> = (element: LayoutElementContent) => {
    const config = element?.config as LayoutElementContentConfigurationTopTenTags;
    return {
      data: config?.tags as unknown as Tag,
    };
  };
}
