import { Injectable } from '@angular/core';
import { DataExtractorFunction, LayoutDataExtractorService } from '../definitions';
import { LayoutElementContent, LayoutElementContentConfigurationVideoEx, VideoEx } from '../../definitions';

@Injectable()
export class VideoExExtractor implements LayoutDataExtractorService<VideoEx | undefined> {
  extractData: DataExtractorFunction<VideoEx | undefined> = (element: LayoutElementContent) => {
    const config = element?.config as LayoutElementContentConfigurationVideoEx;
    return {
      data: {
        ...config,
        htmlContent: {
          mobile: config.htmlContent.desktop,
          desktop: config.htmlContent.desktop,
        },
      },
    };
  };
}
