import { Injectable } from '@angular/core';
import { DataExtractorFunction, LayoutDataExtractorService } from '../definitions';
import { LayoutElementContent, LayoutElementContentArticle, LayoutElementContentConfigurationTabs, Tabs } from '../../definitions';
import { LayoutDataExtractor } from '../../utils';

@Injectable()
export class TabsExtractor implements LayoutDataExtractorService<Tabs[] | undefined> {
  extractData: DataExtractorFunction<Tabs[] | undefined> = (element: LayoutElementContent) => {
    const config = element.config as LayoutElementContentConfigurationTabs;

    if (!config) {
      return;
    }

    const legacyExtractor = new LayoutDataExtractor();
    const tabs = config?.tabs?.map((tab) => {
      const element = {
        config: {
          selectedArticles: tab?.selectedArticles,
          layoutElementId: tab?.id,
        },
      } as Partial<LayoutElementContentArticle>;
      const articles = legacyExtractor.getArticleArrayData(element as LayoutElementContentArticle);
      return {
        id: tab?.id,
        region: tab?.title,
        regionSlug: tab?.slug,
        topArticles: articles?.slice(0, 3),
        bottomArticles: articles?.slice(3),
        tags: tab?.tags,
      };
    });

    return {
      data: tabs as Tabs[],
    };
  };
}
