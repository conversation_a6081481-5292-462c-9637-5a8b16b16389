import { Injectable } from '@angular/core';
import { FakeBool, LayoutElementContent, LayoutElementContentConfigurationQuiz, Quiz, QuizAnswer, QuizQuestion } from '../../definitions';

import { DataExtractorFunction, LayoutDataExtractorService } from '../definitions';

@Injectable()
export class QuizExtractor implements LayoutDataExtractorService<Quiz | undefined> {
  extractData: DataExtractorFunction<Quiz | undefined> = (element: LayoutElementContent) => {
    const conf = element.config as LayoutElementContentConfigurationQuiz;

    if (!conf || !conf?.selectedQuiz?.data) return;
    const quizData: Quiz = {
      ...conf.selectedQuiz?.data,
      ...conf.selectedQuiz?.original,
      ratings: conf.selectedQuiz?.data?.ratings,
      questions: conf.selectedQuiz?.data?.questions
        ?.map((question: QuizQuestion, index: number) => {
          return {
            ...question,
            image: question?.thumbnailUrl || (question as { image: { thumbnailUrl: string } })?.image?.thumbnailUrl,
          };
        })
        .map((question) => {
          return {
            ...question,
            answers: question.answers.map((answer: QuizAnswer) => {
              return {
                ...answer,
                isCorrect: (answer?.isCorrect as FakeBool) === '1',
              };
            }),
          };
        }),
    };

    return {
      data: quizData,
    };
  };
}
