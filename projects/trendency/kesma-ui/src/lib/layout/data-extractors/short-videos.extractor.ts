import { Injectable } from '@angular/core';
import { LayoutElementContent, LayoutElementContentConfigurationShortVideos, VideoCard } from '../../definitions';
import { LayoutDataExtractorService } from '../definitions';

@Injectable()
export class ShortVideosExtractor implements LayoutDataExtractorService<VideoCard[] | undefined> {
  extractData(element: LayoutElementContent) {
    const conf = element.config as LayoutElementContentConfigurationShortVideos;

    let shortVideos = [];

    if (!conf) {
      return;
    }

    shortVideos = conf?.selectedShortVideos
      .filter((item) => item !== null)
      .map(({ data, original, id }) => {
        return {
          id,
          title: data?.title || original?.title,
          thumbnail: { url: data?.thumbnailUrl || original?.coverImage?.thumbnailUrl },
          length: data?.length || original?.length,
          slug: data?.slug || original?.slug,
        };
      });

    return {
      data: shortVideos,
      meta: {
        extractedBy: ShortVideosExtractor.name,
      },
    };
  }
}
