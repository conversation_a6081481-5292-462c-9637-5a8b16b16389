import { ChangeDetectionStrategy, ChangeDetectorRef, Component, HostBinding, Input, OnChanges, OnInit, SimpleChanges, TemplateRef } from '@angular/core';
import {
  Advertisement,
  BlockHeaderTemplateParams,
  BlockWrapperTemplateData,
  BreakingNews,
  LayoutContentItemInnerWrapperTemplateData,
  LayoutContentItemWrapperTemplateData,
  LayoutContentParams,
  LayoutElement,
  LayoutElementColumn,
  LayoutElementContent,
  LayoutElementContentAd,
  LayoutElementContentConfiguration,
  LayoutElementContentType,
  LayoutElementRow,
  LayoutElementType,
  LayoutPageType,
  SecondaryFilterAdvertType,
} from '../../../definitions';
import { AdvertisementAdoceanStoreService } from '../../../services';
import { LayoutDataExtractor } from '../../../utils';
import { ExtractorFacade } from '../../services';
import { UtilService } from '@trendency/kesma-core';
import { Ng<PERSON>lass, NgFor, NgIf, NgTemplateOutlet } from '@angular/common';
import { provideLayoutDataExtractors } from '../../utils';
import { EXTRACTOR_CONFIG } from '../../extractor-config';
import { Observable } from 'rxjs';

@Component({
  selector: 'kesma-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgTemplateOutlet, NgFor, NgIf, NgClass],
  providers: provideLayoutDataExtractors(EXTRACTOR_CONFIG),
})
export class LayoutComponent implements OnInit, OnChanges {
  @Input() adPageType = '';
  @Input() isAdDisabled?: boolean;
  @Input() secondaryFilterType?: SecondaryFilterAdvertType;
  @Input() secondary_pageType?: string;
  @Input() structure: LayoutElementRow[] = [];
  @Input() configuration: LayoutElementContentConfiguration[] = [];
  @Input() layoutType: LayoutPageType = LayoutPageType.HOME;
  @Input() breakingNews: BreakingNews[] = [];
  @Input() contentComponentsRef: TemplateRef<LayoutContentParams> | null = null;
  /**
   * Wrappers give additional options to externally add templates that can add different wrappers for layout components.
   * This is currently only used for the layout editor but you can use this for your own liking.
   */
  @Input() contentComponentWrapperRef?: TemplateRef<LayoutContentItemWrapperTemplateData>;
  /**
   * This wrapper is used for iterable items (for example individual articles in an article card component).
   */
  @Input() contentComponentInnerWrapperRef?: TemplateRef<LayoutContentItemInnerWrapperTemplateData>;
  /**
   * Wraps all the block titles with a custom provided wrapper to register custom functionalities.
   */
  @Input() blockTitleWrapperRef?: TemplateRef<BlockWrapperTemplateData>;
  @Input() blockTitleRef: TemplateRef<BlockHeaderTemplateParams> | null = null;
  @Input() blockSeparatorRef: TemplateRef<BlockHeaderTemplateParams> | null = null;
  @Input() blockTitleAfterComponentRef: TemplateRef<BlockHeaderTemplateParams> | null = null;
  @Input() fullWidthCustomBackground?: boolean;
  @Input() advertsArray$: Observable<Advertisement[]> = this.adStoreAdo.advertisemenets$;
  @Input() editorFrameSize?: 'desktop' | 'mobile';
  dataMapped = false;
  adZones: Advertisement[] = [];
  contentCache: Record<string, any> = {};
  dataExtractor: LayoutDataExtractor = new LayoutDataExtractor();
  readonly LayoutPageTypes = LayoutPageType;
  readonly LayoutElementContentType = LayoutElementContentType;

  constructor(
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly utilsService: UtilService,
    protected readonly extractorFacade: ExtractorFacade
  ) {
    this.dataExtractor.contentCache = this.contentCache;
    this.dataExtractor.breakingNews = this.breakingNews;
  }

  @HostBinding('class.sidebar') get isSidebar() {
    return this.layoutType === LayoutPageType.SIDEBAR;
  }

  ngOnInit(): void {
    if (this.structure && this.configuration) {
      this.mapNode(this.structure, false);
      this.dataMapped = true;
    }
    this.advertsArray$.subscribe((ads) => {
      this.adZones = ads;
      this.initADZones(this.structure, this.isAdDisabled);
      this.changeRef.detectChanges();
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      (!changes['configuration']?.isFirstChange() || !changes['structure']?.isFirstChange()) &&
      (changes['configuration']?.currentValue || changes['structure']?.currentValue)
    ) {
      this.dataMapped = false;
      this.mapNode(this.structure, false);
      this.dataMapped = true;
      this.initADZones(this.structure, this.isAdDisabled);
    }
  }

  initADZones(elements: LayoutElement[], isAdDisabled?: boolean): void {
    if (!this.utilsService.isBrowser() || isAdDisabled) {
      return;
    }
    if (elements) {
      elements.forEach((el) => {
        // recursions on row & column children
        if (el.type === LayoutElementType.Row || el.type === LayoutElementType.Column) {
          this.initADZones((el as LayoutElementRow | LayoutElementColumn).elements);
          return;
        }

        if ((el as LayoutElementContent).contentType === LayoutElementContentType.Ad) {
          (el as LayoutElementContentAd).ad = this.getAd(el as LayoutElementContentAd);
        }
      });
    }
  }

  getConfig(element: LayoutElementContent): LayoutElementContentConfiguration | undefined {
    return this.configuration.find((c) => c?.layoutElementId === element?.id);
  }

  mapNode(elements: (LayoutElementRow | LayoutElementColumn | LayoutElementContent)[], hasParent: boolean): void {
    elements.forEach((el) => {
      // recursions on row & column children
      if (el.type === LayoutElementType.Row || el.type === LayoutElementType.Column) {
        el.hasParent = hasParent;
        this.mapNode((el as LayoutElementRow | LayoutElementColumn).elements, true);
        return;
      }

      el.columnCount = this.getContentElementColumnRate(el.contentLength, el);
      el.config = this.getConfig(el);
      el.hasParent = hasParent;
      el.extractorData = this.extractorFacade.extractData(el);
      el.extractorResult = this.extractorFacade.extract(el);
      el.iterator = [
        LayoutElementContentType.KompostBlock,
        LayoutElementContentType.YessfactorBlock,
        LayoutElementContentType.MEDIA_PANEL,
        LayoutElementContentType.MOST_VIEWED,
        LayoutElementContentType.INGREDIENT,
        LayoutElementContentType.RECIPE_SWIPER,
        LayoutElementContentType.GUARANTEE_BOX,
        LayoutElementContentType.RIPOST7_BLOCK,
        LayoutElementContentType.OFFER_BOX,
        LayoutElementContentType.BAYER_BLOG,
        LayoutElementContentType.GASTRO_EXPERIENCE_RECOMMENDATION,
        LayoutElementContentType.GASTRO_OCCASION_RECOMMENDER,
        LayoutElementContentType.COLUMN_BLOCK,
        LayoutElementContentType.PROGRAM_LIST,
        LayoutElementContentType.SHORT_VIDEOS,
      ].includes((el?.secondaryContentType || el?.contentType) as LayoutElementContentType)
        ? [1]
        : new Array(el.contentLength).fill(0).map((_, i) => i);
    });
  }

  getContentElementColumnRate(contentLength: number, layoutElement?: LayoutElementContent): number {
    const ONLY_ONE_BLOCK = 1;
    if (
      layoutElement?.secondaryContentType === LayoutElementContentType.KompostBlock ||
      layoutElement?.secondaryContentType === LayoutElementContentType.YessfactorBlock ||
      layoutElement?.secondaryContentType === LayoutElementContentType.RIPOST7_BLOCK ||
      layoutElement?.contentType === LayoutElementContentType.BestRecommender ||
      layoutElement?.contentType === LayoutElementContentType.GUARANTEE_BOX ||
      layoutElement?.contentType === LayoutElementContentType.OFFER_BOX ||
      layoutElement?.contentType === LayoutElementContentType.RECIPE_SWIPER ||
      layoutElement?.contentType === LayoutElementContentType.INGREDIENT ||
      layoutElement?.contentType === LayoutElementContentType.ARTICLE_SLIDER ||
      layoutElement?.contentType === LayoutElementContentType.MOST_VIEWED
    ) {
      return Math.round(12 / ONLY_ONE_BLOCK);
    }

    // TODO: review with better case handling
    return Math.round(12 / contentLength);
  }

  getAd(element: LayoutElementContentAd): Advertisement | undefined {
    const zoneIds = this.adStoreAdo.separateAdsByMedium(
      this.adZones,
      this.adPageType,
      [element?.bannerName],
      this.secondaryFilterType,
      this.secondary_pageType
    );

    if (element?.medium === 'desktop' && zoneIds?.desktop) {
      return zoneIds?.desktop[element?.bannerName];
    }

    if (element?.medium === 'mobile' && zoneIds?.mobile) {
      return zoneIds?.mobile[element?.bannerName];
    }

    return undefined;
  }

  trackByItemId(index: number, item: LayoutElement): string {
    return item.id + index;
  }
}
