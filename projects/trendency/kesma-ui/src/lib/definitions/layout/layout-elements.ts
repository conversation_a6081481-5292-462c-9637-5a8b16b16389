import { Advertisement, AdvertisementBannerName } from '../advertisement.definitions';
import { ExtendedTag } from '../extended-tag';
import { SourceSelectValue, TagLink } from '../kesma-core.definitions';
import {
  AutoFill,
  BlockTitle,
  BrandingArticle,
  Config,
  LayoutArticle,
  LayoutArticleData,
  LayoutElementContentType,
  LayoutElementType,
  LayoutGalleryImage,
  LayoutImage,
  LayoutTurpiCardData,
  LayoutWysiwygFormControl,
  RecipeCardData,
  SelectedArticle,
  SelectedBreakingRec,
  SelectedDossier,
  SelectedVideo,
} from '../layout.definitions';
import { Quiz, QuizQuestion, QuizRating } from '../quiz.definitions';
import { TrendingTag } from '../trending-tag';
import { BackendLayoutVotingArticle, BackendVoteData } from '../voting.definitions';
import { Author, Tag } from '../article-card.definitions';
import { IngredientData } from '../ingredient.definition';
import { RecipeCard, RecipeDifficulty } from '../recipe.definitions';
import { Maestro } from '../maestro-box.definitions';
import { HighlightedSelection } from '../highlighted-selection.definitions';
import { TapeDataType } from '../tape.definitions';
import { LayoutDataExtractorResult } from '../../layout';
import { OlimpiaHungarianTeam, OlimpicPortalEnum } from '../olimpic/olimpic.definitions';
import { FakeBool } from '../common.definitions';
import { BackendMultiVoteData } from '../multi-vote.definitions';
import { FocusPointUrlWithAspectRatio } from '../focus-point.definitions';
import { GastroExperienceCard } from '../gastro-experience-card.definitions';
import { PriorityContentFlags } from '../seo.definitions';

export interface OverwriteImage {
  url?: {
    thumbnail?: string;
    fullSize?: string;
  };
}

export interface RecursiveLayoutElement extends LayoutStruct {
  hasImg?: boolean;
  hasDate?: boolean;
  hasLead?: boolean;
  styleId?: number;
  hasTitle?: boolean;
  contentType: string;
  hasReadTime?: boolean;
  configurable: boolean;
  previewImage: string;
  contentLength: number;
  config?: Config;
  medium?: string;
  bannerName?: string;
}

export interface LayoutStruct extends LayoutElement {
  elements: RecursiveLayoutElement[];
}

export interface LayoutElement {
  id: string;
  type: LayoutElementType;
  hideMobile: boolean;
  withBlockTitle: boolean;
  blockTitle?: BlockTitle;
  mobileOrder?: number;
}

export interface LayoutElementRow extends LayoutElement {
  type: LayoutElementType.Row;
  elements: (LayoutElementColumn | LayoutElementContent)[]; // szumma width mindhárom properyn 100 többszöröse kell legyen.
  // ha content típus van soros elrendezésben, akkor a 12/contentLength colt kap minden elem
  backgroundColor?: string; // CSS rgba
  hasLine?: boolean;
  hasParent?: boolean;
}

// nem muszaj bootstrap, %-al is csinalhatjuk, meg nagyobb customizalas
export interface LayoutElementColumn extends LayoutElement {
  type: LayoutElementType.Column;
  widthDesktop: number;
  elements: (LayoutElementRow | LayoutElementContent)[];
  hasRightMargin?: boolean;
  hasLeftMargin?: boolean;
  marginBorderColor?: 'green' | 'gray';
  hasParent?: boolean;
}

export interface LayoutElementContent extends LayoutElement {
  type: LayoutElementType.Content;
  secondaryContentType?: LayoutElementContentType;
  contentLength: number;
  contentType: LayoutElementContentType;
  previewImage: string;
  // TODO: use these two properties
  editable?: boolean;
  configurable?: boolean;
  config?: LayoutElementContentConfiguration;
  extractorData?: unknown;
  extractorResult?: LayoutDataExtractorResult;
  columnCount?: number;
  iterator?: number[];
  mobileOrder?: number;
  hasParent?: boolean;
}

export interface LayoutElementContentFinalCountdown extends LayoutElementContent {
  contentType: LayoutElementContentType.FinalCountdown;
  styleId: number;
}

export interface LayoutElementContentRecipeCategorySelect extends LayoutElementContent {
  contentType: LayoutElementContentType.RECIPE_CATEGORY_SELECT;
  styleId: number;
}

export interface LayoutElementContentMaestroBox extends LayoutElementContent {
  contentType: LayoutElementContentType.MAESTRO_BOX;
  styleId: number;
}

export interface LayoutElementContentSelection extends LayoutElementContent {
  contentType: LayoutElementContentType.SELECTION;
  styleId: number;
}

export interface LayoutElementContentHighlightedSelection extends LayoutElementContent {
  contentType: LayoutElementContentType.HIGHLIGHTED_SELECTION;
  styleId: number;
}

export interface LayoutElementContentTextBox extends LayoutElementContent {
  contentType: LayoutElementContentType.TEXT_BOX;
  styleId: number;
}

export interface LayoutElementContentTurpiCard extends LayoutElementContent {
  contentType: LayoutElementContentType.TURPI_CARD;
  styleId: number;
  configurable: true;
}

export interface LayoutElementContentIngredient extends LayoutElementContent {
  contentType: LayoutElementContentType.INGREDIENT;
  styleId: number;
  configurable: true;
}

export interface LayoutElementContentMoreArticles extends LayoutElementContent {
  contentType: LayoutElementContentType.MORE_ARTICLES;
  styleId: number;
}

export interface LayoutElementContentColumnBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.COLUMN_BLOCK;
  styleId: number;
}

export interface LayoutElementContentTurpiBox extends LayoutElementContent {
  contentType: LayoutElementContentType.TURPI_BOX;
  styleId: number;
}

export interface LayoutElementContentHelloBudapest extends LayoutElementContent {
  contentType: LayoutElementContentType.HelloBudapest;
  configurable: true;
  styleId?: number;
  hasTitle: boolean;
  hasLead: boolean;
  hasImg?: boolean; // it's not present always in reality -> mapped later
  canHaveCustomTag?: boolean; // TODO: set all non domain specific prop to optional
}

export interface LayoutElementContentArticle extends LayoutElementContent {
  contentType: LayoutElementContentType.Article;
  styleId: number;
  hasTitle: boolean;
  hasLead: boolean;
  hasImg: boolean;
  hasDate: boolean;
  hasReadTime: boolean;
  fontSize: number;
  withHorizontalSeparator?: boolean;
}

export interface LayoutElementContentDetections extends LayoutElementContent {
  contentType: LayoutElementContentType.DETECTIONS;
  configurable: true;
  parentWidth: number;
  minParentWidth: number;
  styleId: number;
}

export interface LayoutElementContentMinuteToMinute extends LayoutElementContent {
  contentType: LayoutElementContentType.MinuteToMinute;
  styleId: number;
  configurable: true;
  hasTitle: boolean;
  hasLead: boolean;
  hasImg: boolean;
  hasDate: boolean;
  hasReadTime: boolean;
}

export interface LayoutElementContentFastNews extends LayoutElementContent {
  contentType: LayoutElementContentType.FastNews;
  styleId: number;
  hasTitle: boolean;
  hasLead: boolean;
  hasImg: boolean;
  hasDate: boolean;
  hasReadTime: boolean;
}

export interface LayoutElementContentRecipe extends LayoutElementContent {
  contentType: LayoutElementContentType.RECIPE;
  styleId: number;
  hasBackground: boolean;
  configurable: true;
  hasTitle?: boolean;
  hasImg?: boolean;
  hasImage?: boolean;
}

export interface LayoutElementContentExperience extends LayoutElementContent {
  contentType: LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION;
  styleId: number;
  configurable: true;
}

export interface LayoutElementContentGuaranteeBox extends LayoutElementContent {
  contentType: LayoutElementContentType.GUARANTEE_BOX;
  styleId: number;
  configurable: true;
}

export interface LayoutElementContentFreshBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.FreshBlock;
  styleId: number;
  hasTitle: boolean;
  hasLead: boolean;
}

export interface LayoutElementContentBreaking extends LayoutElementContent {
  contentType: LayoutElementContentType.Breaking;
  hideMobile: boolean;
}

export interface LayoutElementContentOpinion extends LayoutElementContent {
  contentType: LayoutElementContentType.Opinion;
  styleId: number;
  hasTitle: boolean;
  hasLead: boolean;
  hasImg: boolean;
  hasDate: boolean;
  hasReadTime: boolean;
  hasWriterName: boolean;
  hasWriterImg: boolean;
  withHorizontalSeparator?: boolean;
}

export interface LayoutElementContentVideo extends LayoutElementContent {
  contentType: LayoutElementContentType.Video;
  styleId: number;
  hasTitle: boolean;
  hasLead: boolean;
  hasCoverImage: boolean;
  hasWatchTime: boolean;
}

export interface LayoutElementContentDossier extends LayoutElementContent {
  contentType: LayoutElementContentType.Dossier;
  styleId: number;
  dossierOptions: {
    maxSecondaryArticleCount: number;
  }[]; // The length of this array defines how many dossiers are in one box
}

export interface LayoutElementContentNotebook extends LayoutElementContent {
  contentType: LayoutElementContentType.Note;
  styleId: number;
  hasAuthorImg: boolean;
  hasAuthorName: boolean;
  hasImg: boolean;
  hasLead: boolean;
  hasTitle: boolean;
  hideMobile: boolean;
}

export interface LayoutElementContentAd extends LayoutElementContent {
  contentType: LayoutElementContentType.Ad;
  medium: string;
  bannerName: AdvertisementBannerName;
  zoneId?: string;
  ad?: Advertisement;
}

export interface LayoutElementContentStockChart extends LayoutElementContent {
  contentType: LayoutElementContentType.StockChart;
}

// max 4. explicit
export interface LayoutElementContentPodcastBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.PodcastBlock;
}

export interface LayoutElementContentNewsletterBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.NewsletterBlock;
}

export interface LayoutElementContentFreshNews extends LayoutElementContent {
  contentType: LayoutElementContentType.FreshNews;
  configurable: true;
}

export interface LayoutElementContentLinkList extends LayoutElementContent {
  contentType: LayoutElementContentType.LinkList;
  configurable: true;
}

export interface LayoutElementContentVote extends LayoutElementContent {
  contentType: LayoutElementContentType.Vote;
  configurable: true;
}
export interface LayoutElementContentSponsoredVote extends LayoutElementContent {
  contentType: LayoutElementContentType.SPONSORED_VOTE;
  configurable: true;
}

export interface LayoutElementContentMultiVote extends LayoutElementContent {
  contentType: LayoutElementContentType.MULTI_VOTE;
  configurable: true;
}

export interface LayoutElementContentTrendingTagsBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.TrendingTagsBlock;
  configurable: true;
}

export interface LayoutElementContentTagBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.TagBlock;
  configurable: true;
}

export interface LayoutElementContentWysiwyg extends LayoutElementContent {
  contentType: LayoutElementContentType.Wysiwyg;
  configurable: true;
}

export interface LayoutElementContentImage extends LayoutElementContent {
  contentType: LayoutElementContentType.Image;
  configurable: true;
}

export interface LayoutElementContentGallery extends LayoutElementContent {
  contentType: LayoutElementContentType.Gallery;
  styleId: number;
  hasTitle: boolean;
  hasImage: boolean;
  configurable: true;
}

export interface LayoutElementContentQuiz extends LayoutElementContent {
  contentType: LayoutElementContentType.Quiz;
  styleId: number;
  hasTitle: boolean;
  hasImage: boolean;
  configurable: true;
}
export interface LayoutElementContentSponsoredQuiz extends LayoutElementContent {
  contentType: LayoutElementContentType.SponsoredQuiz;
  styleId: number;
  hasTitle: boolean;
  hasImage: boolean;
  configurable: true;
}

export interface LayoutElementContentVideoBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.VideoBlock;
  styleId: number;
  hasTitle: boolean;
  hasImage: boolean;
  configurable: true;
}

export interface LayoutElementContentBrandingBox extends LayoutElementContent {
  contentType: LayoutElementContentType.BrandingBox;
  brand: string;
  numberOfArticles: number;
  configurable: true;
}

export interface LayoutElementContentDossierList extends LayoutElementContent {
  contentType: LayoutElementContentType.DossierList;
  styleId: number;
  configurable: true;
}

export interface LayoutElementContentDataBank extends LayoutElementContent {
  contentType: LayoutElementContentType.DATA_BANK;
  configurable: true;
}

export interface LayoutElementContentKompostYessFactorBlock extends Omit<LayoutElementContent, 'secondaryContentType'> {
  secondaryContentType: LayoutElementContentType;
  configurable: true;
}

export interface LayoutElementContentVisegradPost extends LayoutElementContent {
  contentType: LayoutElementContentType.VisegradPost;
  numberOfArticles?: number;
  configurable: true;
}

export interface LayoutElementContentCultureNation extends LayoutElementContent {
  contentType: LayoutElementContentType.CultureNation;
  configurable: true;
}

export interface LayoutElementContentHtmlEmbed extends LayoutElementContent {
  contentType: LayoutElementContentType.HtmlEmbed;
  configurable: true;
}

export interface LayoutElementContentBestRecommender extends LayoutElementContent {
  contentType: LayoutElementContentType.BestRecommender;
  configurable: true;
}

export interface LayoutElementContentCategoryStepper extends LayoutElementContent {
  contentType: LayoutElementContentType.CategoryStepper;
  configurable: true;
}

export interface LayoutElementContentConfiguration {
  layoutElementId: string;
}

export interface LayoutElementContentConfigurationTrendingTagsBlock extends LayoutElementContentConfiguration {
  trendingTagsMain: TrendingTag[];
  trendingTagsSecondary: TrendingTag[];
}

export interface LayoutElementContentConfigurationTagBlock extends LayoutElementContentConfiguration {
  tags: ExtendedTag[];
}

export interface LayoutElementContentConfigurationDetections extends LayoutElementContentConfiguration {
  selectedDetections: SourceSelectValue[];
}

export interface LayoutElementContentConfigurationFastNews extends LayoutElementContentConfiguration {
  selectedFastNews: {
    id: string;
    overwrite: {
      title: string;
      image: OverwriteImage;
      lead: string;
      customTag?: string;
    };
    original?: LayoutArticle;
    data?: LayoutArticleData;
  }[];
  autoFill: AutoFill;
  title?: string;
  backgroundColor: string;
  foregroundColor: string;
}

export interface LayoutElementContentConfigurationRecipe extends LayoutElementContentConfiguration, PriorityContentFlags {
  selectedRecipes: LayoutConfigurationRecipe[];
  autoFill: AutoFill;
  title?: string;
}

export interface LayoutElementContentConfigurationGuaranteeBox extends LayoutElementContentConfiguration, PriorityContentFlags {
  selectedArticlesAndRecipes: (LayoutConfigurationRecipe | SelectedArticleConf)[];
  blockTitle: {
    url: string;
    name?: string;
  };
}

export interface LayoutConfigurationRecipe {
  id: string;
  overwrite: {
    title: string;
    image: any;
    alt: string;
    lead: string;
    customTag?: string;
  };
  original?: RecipeCard & {
    id: string;
    coverImage: {
      fullSizeUrl: string;
      thumbnailUrl: string;
    };
    description: string;
    allergics: {
      title: string;
      slug: string;
    }[];
    // recipeIngredients: [];
    tags: {
      slug: string;
      title: string;
    }[];
    madeForPeople: number;
    prepareTime: number;
    cookTime: number;
    totalTime: number;
    isHighlightInTape: boolean;
    highlightInTapeFromDate: string;
    highlightInTapeToDate: string;
    sentByUser: string;
    acceptedAt: string;
    difficulty: RecipeDifficulty | string;
    allowComments: boolean;
    energy: number;
    protein: number;
    fat: number;
    carbohydrate: number;
    mmeWarranty: boolean;
    containsVideo: boolean;
    isNotifiable: boolean;
    notificationSentAt: string;
    publicAuthor: {
      avatar: {
        fullSizeUrl: string;
        thumbnailUrl: string;
      };
      fullName: string;
      id: string;
      isMaestroAuthor: boolean;
      slug: string;
    };
    publishDate: string;
    user: {
      id: string;
      slug: string;
      fullName: string;
      publicAuthorName: string;
    };
  };
  data?: RecipeCard & {
    madeForPeople: number;
    prepareTime: number;
    mmeWarranty: boolean;
    containsVideo: boolean;
    imgFullUrl?: string;
    imgThumbUrl?: string;
    user?: {
      id: string;
      slug: string;
      fullName: string;
      publicAuthorName: string;
    };
  };
  title?: string;
}

export interface SelectedArticleConf extends SelectedArticle {
  id: string;
  // original?: LayoutArticle;
  data?: LayoutArticleData;
  blockTitle?: BlockTitle;
}

export interface LayoutElementContentConfigurationArticle extends LayoutElementContentConfiguration, PriorityContentFlags {
  selectedArticles: SelectedArticleConf[];
  autoFill: AutoFill;
  title?: string;
  backgroundColor: string;
  foregroundColor: string;
  titleBackgroundColor?: string;
}

export interface LayoutElementContentConfigurationPodcast extends LayoutElementContentConfiguration {
  selectedPodcasts: {
    id: string;
    original?: LayoutArticle;
    data?: LayoutArticleData;
  }[];
  autoFill: AutoFill;
  doAutoFill: boolean;
}

export interface LayoutElementContentBreakingBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.Breaking;
  styleId: number;
  hasTitle: boolean;
  hasLead: boolean;
  hasImg?: boolean; // Changed to optional as in reality this is missing many times and only added at later mapping
  hasImage?: boolean; // FIXME added for type compatibilty as dialog uses `hasImage` and it's mapped to `hasImg`
  hasDate?: boolean;
  hasReadTime?: boolean;
  canHaveCustomTag?: boolean; // TODO: set all non domain specific prop to optional
  configurable: true;
}

export interface LayoutElementContentConfigurationFinalCountdown extends LayoutElementContentConfiguration {
  deadLine: string | Date;
  endText: string;
  backgroundColor: string;
  color: string;
  size: number;
}

export interface LayoutElementContentConfigurationHelloBudapest extends LayoutElementContentConfiguration {
  selectedArticles: {
    id: string;
    title: string;
    original?: any;
    data?: any;
  }[];
  autoFill: {
    filterTags: SourceSelectValue[];
    filterSports: SourceSelectValue[];
    filterDossiers: SourceSelectValue[];
  };
}

export interface LayoutElementContentConfigurationOpinion extends LayoutElementContentConfiguration {
  selectedOpinions: {
    id: string;
    overwrite: {
      title: string;
      image: OverwriteImage;
      lead: string;
      excerpt?: string;
      customTag?: string;
      thumbnailFocusedImages?: FocusPointUrlWithAspectRatio;
    };
    original?: LayoutArticle;
    data?: LayoutArticleData;
  }[];
  autoFill: AutoFill;
  title?: string;
}

export interface LayoutElementContentConfigurationBlog extends LayoutElementContentConfiguration {
  selectedBlogs: {
    id: string;
    overwrite: {
      title: string;
      image: OverwriteImage;
      lead: string;
      excerpt?: string;
      customTag?: string;
      thumbnailFocusedImages?: FocusPointUrlWithAspectRatio;
    };
    original?: LayoutArticle;
    data?: LayoutArticleData;
  }[];
  autoFill: AutoFill;
  title?: string;
}

export interface LayoutElementContentConfigurationFreshBlock extends LayoutElementContentConfiguration {
  selectedArticles: {
    id: string;
    overwrite: {
      title: string;
      image: OverwriteImage;
      lead: string;
      customTag?: string;
    };
    original?: LayoutArticle;
    data?: LayoutArticleData;
  }[];
  autoFill: AutoFill;
}

export interface LayoutElementContentConfigurationDossier extends LayoutElementContentConfiguration {
  selectedDossiers: SelectedDossier[];
}

export interface LayoutElementContentConfigurationTextBox extends LayoutElementContentConfiguration {
  text: string;
}

export interface LayoutElementContentConfigurationTurpiCard extends LayoutElementContentConfiguration {
  selectedBestPractices: {
    author: Author;
    description: string;
    data?: LayoutTurpiCardData;
  }[];
}

export interface LayoutElementContentConfigurationIngredient extends LayoutElementContentConfiguration {
  selectedIngredients: IngredientData[];
}

export interface LayoutElementContentConfigurationThematicRecommender extends LayoutElementContentConfiguration {
  experiences: GastroExperienceCard[];
  selectedCategory: any;
}

export interface LayoutElementContentConfigurationRecipeCategorySelect extends LayoutElementContentConfiguration {
  blockTitle?: string;
  type: string;
  recipeCategories: {
    title: string;
    recipes: RecipeCardData[];
  }[];
}

export interface LayoutElementContentConfigurationMaestroBox extends LayoutElementContentConfiguration {
  title: string;
  description: string;
  authors: (Maestro & { id: string })[];
}

export interface LayoutElementContentConfigurationSelection extends LayoutElementContentConfiguration {
  sortingBoxData: {
    title: string;
    description: string;
    highlightedImage: {
      altText: string;
      title: string;
      url: { thumbnail: string; fullSize: string };
    };
    showMoreUrl: string;
  };
  selectedSelectionItems: { data: any; id: string; contentType?: TapeDataType }[];
  selection?: {
    id: string;
    slug: string;
    title: string;
  };
}

export interface LayoutElementContentConfigurationHighlightedSelection extends LayoutElementContentConfiguration {
  selection?: HighlightedSelection;
}

export interface LayoutElementContentConfigurationMoreArticles extends LayoutElementContentConfiguration {
  selectedUrl: {
    urlName: string;
    url: string;
    color: string;
  };
}

export interface LayoutElementContentMapRecommendations extends LayoutElementContent {
  contentType: LayoutElementContentType.MAP_RECOMMENDATIONS;
  styleId: number;
  configurable: false;
  mapConfig?: Record<string, any>[];
}

export interface LayoutElementContentConfigurationGallery extends LayoutElementContentConfiguration {
  selectedGalleries: {
    id: string;
    title: string;
    original?: {
      createdAt: Date;
      columns?: { title: string; slug: string }[];
      description: string;
      highlightedImage: LayoutGalleryImage;
      id: string;
      images: LayoutGalleryImage[];
      publicDate: Date;
      title: string;
      slug: string;
      photographer: string;
      tags: Tag[];
      isAdult?: boolean;
    };
    data?: {
      galDescription: string;
      galSlug: string;
      galTitle: string;
      id: string;
      imgAltText?: string;
      imgCaption?: string;
      imgCreatedAt: string;
      imgFullUrl: string;
      imgId: string;
      imgPhotographer?: string;
      imgSource?: string;
      imgThumbUrl: string;
      imgTitle?: string;
      preTitle: string;
      isAdult?: boolean | FakeBool;
      publicDate?: string;
    };
  }[];
}

export interface LayoutElementContentConfigurationQuiz extends LayoutElementContentConfiguration {
  selectedQuiz: {
    id: string;
    title: string;
    data: Quiz;
    original?: {
      isActive: boolean;
      questions: QuizQuestion[];
      ratings: QuizRating[];
      title: string;
    };
  };
}

export interface LayoutElementContentConfigurationVideoBlock extends LayoutElementContentConfiguration {
  selectedVideos: SelectedVideo[];
  autoFill: AutoFill;
}

export interface LayoutElementContentConfigurationShortVideos extends LayoutElementContentConfiguration {
  selectedShortVideos: SelectedVideo[];
  autoFill: AutoFill;
}

export interface LayoutElementContentConfigurationConference extends LayoutElementContentConfiguration {
  title: string;
  lead: string;
  date: string;
  location: string;
  url: string;
  indexImage1: LayoutImage;
  indexImage2: LayoutImage;
}

export interface LayoutElementContentConfigurationDossierList extends LayoutElementContentConfiguration {
  doAutoFill: boolean;
  selectedDossiers: {
    id: string;
    overwriteTitle: string;
    original?: any;
    data?: any;
  }[];
}

export interface LayoutElementContentConfigurationBrandingBox extends LayoutElementContentConfiguration {
  selectedBrandingBoxes: BrandingArticle[];
  tagLinkList: TagLink[];
}

export interface LayoutElementContentConfigurationFreshNews extends LayoutElementContentConfiguration {
  autoFill: AutoFill;
}

export interface LayoutElementContentConfigurationVote extends LayoutElementContentConfiguration {
  selectedVote: {
    id: string;
    title: string;
    article?: BackendLayoutVotingArticle;
    original?: BackendVoteData & { isActive: boolean };
    data?: BackendVoteData;
  };
  showCount?: boolean;
  showPercent?: boolean;
}

export interface LayoutElementContentConfigurationSponsoredVote extends LayoutElementContentConfigurationVote {
  isAdOceanEnabled: boolean;
  adOceanId?: string;
}

export interface LayoutElementContentConfigurationMultiVote extends LayoutElementContentConfiguration {
  selectedMultiVote: BackendMultiVoteData;
  showCount?: boolean;
  showPercent?: boolean;
}

export interface LayoutElementContentConfigurationWysiwyg extends LayoutElementContentConfiguration {
  formControls: LayoutWysiwygFormControl[];
}

export interface LayoutElementContentConfigurationLinkList extends LayoutElementContentConfiguration {
  linkList: TagLink[];
}

export interface LayoutElementContentConfigurationHtmlEmbed extends LayoutElementContentConfiguration {
  htmlContent: { desktop: string; mobile: string };
}

export interface LayoutElementContentConfigurationHero extends LayoutElementContentConfigurationImage {
  selectedDetections?: ({
    image?: {
      thumbnailUrl?: string;
    };
    user?: {
      id?: string;
      userName?: string;
    };
  } & SourceSelectValue)[];
}

export interface LayoutElementContentConfigurationImage extends LayoutElementContentConfiguration {
  cancelMargin: boolean;
  selectedImage: LayoutImage;
  url: string;
}

export interface LayoutElementContentConfigurationBreaking extends LayoutElementContentConfiguration {
  selectedBreakings: SelectedBreakingRec[];
  autoFill: AutoFill;
  doAutoFill: boolean;
}

export interface LayoutElementContentConfigurationDataBank extends LayoutElementContentConfiguration {
  selectedCompetitions: any[];
}

export interface LayoutElementContentConfigurationBestRecommender extends LayoutElementContentConfiguration {
  selectedColumns: {
    id: string;
    slug?: string;
    title: string;
    activeIconUrl?: string;
    inActiveIconUrl?: string;
    original?: Record<string, any>;
    data?: Record<string, any>;
  }[];
  tabs: {
    selectedArticles: {
      id: string;
      overwrite: {
        title: string;
        image: OverwriteImage;
        lead: string;
        customTag?: string;
      };
      original?: LayoutArticle;
      data?: LayoutArticleData;
    }[];
    autoFill: AutoFill;
  }[];
}

export interface LayoutElementContentConfigurationCategoryStepper extends LayoutElementContentConfigurationBestRecommender {
  interval: number;
}

export interface LayoutElementContentPdfBox extends LayoutElementContent {
  contentType: LayoutElementContentType.PdfBox;
  configurable: true;
  styleId?: string;
}

export interface LayoutElementContentConfigurationPdfBox extends LayoutElementContentConfiguration {
  boxTitle: string;
  title: string;
  lead?: string;
  btnUrl: string;
  target: '_blank' | '_self';
  isOpenNewTab?: boolean;
}

export interface LayoutElementContentNewspaper extends LayoutElementContent {
  contentType: LayoutElementContentType.NEWSPAPER;
  configurable: true;
}

export interface LayoutElementContentConfigurationNewspaper extends LayoutElementContentConfiguration {
  printUrl?: string;
  onlineUrl?: string;
}

export interface LayoutElementContentConfigurationTurpiBox extends LayoutElementContentConfiguration {
  selectedBestPractices?: {
    id?: string;
    title?: string;
    description?: string;
    owner?: string;
    data?: LayoutTurpiCardData;
  }[];
  title?: string;
  description?: string;
  selectedImage?: LayoutImage;
}

export interface LayoutElementContentTripBox extends LayoutElementContent {
  matchesNumber: number;
  tripName: string;
}

export interface LayoutElementContentConfigurationTripBox extends LayoutElementContentConfiguration {
  tripName: string;
  allResult: string;
  matchesNumber: number;
  // matches: ChampionshipSchedule[],
  matches: {
    awayScore: string;
    awayTeam: {
      title: string;
      id: string;
    };
    competition: {
      id: string;
      slug: string;
      title: string;
    };
    homeScore: string;
    homeTeam: {
      title: string;
      id: string;
    };
    id: string;
    scheduleDate: {
      date: Date;
      timezone: string;
      timezone_type: number;
    };
    tvStation: {
      id: string;
      logo: string;
      officialPage: string;
      title: string;
    };
  };
}

export interface LayoutElementContentLatestNews extends LayoutElementContent {
  contentType: LayoutElementContentType.LATEST_NEWS;
  configurable: boolean;
}

export interface LayoutElementContentDidYouKnow extends LayoutElementContent {
  contentType: LayoutElementContentType.DID_YOU_KNOW;
  configurable: true;
  config: LayoutElementContentConfigurationDidYouKnow;
}
export interface LayoutElementContentConfigurationDidYouKnow extends LayoutElementContentConfiguration {
  didYouKnowFilter?: string[];
}

export interface LayoutElementContentSorozatveto extends LayoutElementContent {
  contentType: LayoutElementContentType.Sorozatveto;
  configurable: true;
  config: LayoutElementContentConfigurationSorozatveto;
}

export interface LayoutElementContentConfigurationSorozatveto extends LayoutElementContentConfiguration {
  selectedArticles: {
    id: string;
    overwrite: {
      title: string;
      image: any;
      lead: string;
      customTag?: string;
      preTitle?: string;
    };
    original?: any;
    data?: any;
  }[];

  selectedReview: {
    id: string;
    isActive?: boolean;
    isReviewable?: boolean;
    reviewOrder?: number;
    title: string;
    rowActions?: string[];
    user?: {
      firstname: string;
      lastname: string;
    };
    data: {
      id: string;
      title: string;
      slug: string;
      excerpt?: string;
      lead?: string;
      thumbnailUrl?: string;
      secondaryThumbnailUrl?: string;
      publicAuthor?: {
        id?: string;
        name?: string;
        avatarThumbnailUrl?: string;
      };
      reviewed: {
        id: string;
        title: string;
        slug: string;
        excerpt?: string;
        lead?: string;
        thumbnailUrl?: string;
        secondaryThumbnailUrl?: string;
        publicAuthor?: {
          id?: string;
          name?: string;
          avatarThumbnailUrl?: string;
        };
      };
    };
  };
}

export interface LayoutElementContentConfigurationVisegradPost extends LayoutElementContentConfiguration {
  selectedArticles: SelectedArticle[];
  autoFill: {
    filterArticleSources: { id: string }[];
    orderPriority: string; // date or most popular
  };
}

export interface LayoutElementContentConfigurationLatestNews extends LayoutElementContentConfiguration {
  selectedArticles?: {
    id: string;
    overwrite: {
      title: string;
      image: any;
      lead: string;
    };
    original?: any;
    data?: any;
  }[];
}

export interface LayoutElementContentConference extends LayoutElementContentConfiguration {
  contentType: LayoutElementContentType.CONFERENCE;
  configurable: true;
  config: LayoutElementContentConfigurationConference;
}

export interface LayoutElementContentCountdownBox extends LayoutElementContent {
  contentType: LayoutElementContentType.COUNTDOWN_BOX;
  configurable: true;
  styleId: number;
}

export interface LayoutElementContentConfigurationCountdownBox extends LayoutElementContentConfiguration {
  endDate: Date;
  exactTime: boolean;
  endText: string;
  slug: string;
  mainText?: string;
  subText?: string;
  valueSuffixText?: string;
  logoImage?: {
    url?: {
      fullSize?: string;
    };
  };
  sponsorImage?: {
    url?: {
      fullSize?: string;
    };
  };
  sponsorUrl?: string;
}

export interface LayoutElementContentEbCountdownBlockTitle extends LayoutElementContent {
  contentType: LayoutElementContentType.EB_COUNTDOWN_BLOCK_TITLE;
  configurable: true;
  styleId: number;
}

export interface LayoutElementContentConfigurationEbCountdownBlockTitle extends LayoutElementContentConfiguration {
  title: string;
  slug: string;
}

export interface LayoutElementContentOlimpiaCountdownBlockTitle extends LayoutElementContent {
  contentType: LayoutElementContentType.OLIMPIA_COUNTDOWN_BLOCK_TITLE;
  configurable: true;
  styleId: number;
}

export interface LayoutElementContentConfigurationOlimpiaCountdownBlockTitle extends LayoutElementContentConfiguration {
  title: string;
  slug: string;
}

export interface LayoutElementContentOlimpiaHungarianTeam extends LayoutElementContent {
  contentType: LayoutElementContentType.OLIMPIA_HUNGARIAN_TEAM;
  configurable: true;
  styleId: OlimpicPortalEnum;
}

export interface LayoutElementContentConfigurationOlimpiaHungarianTeam extends LayoutElementContentConfiguration {
  selectedAthletes: OlimpiaHungarianTeam[];
  styleId: OlimpicPortalEnum;
}

export interface LayoutElementContentConfigurationTabs extends LayoutElementContentConfiguration {
  tabs: {
    id: string;
    title: string;
    slug: string;
    selectedArticles: SelectedArticle[];
    tags?: Tag[];
  }[];
}

export interface LayoutElementContentConfigurationNote extends LayoutElementContentConfiguration {
  selectedNotebooks: SelectedArticleConf[];
}

export interface LayoutElementContentConfigurationPublicAuthors extends LayoutElementContentConfiguration {
  authors: Author[];
}

export interface LayoutElementContentConfigurationServicesBox extends LayoutElementContentConfiguration {
  services: {
    title: string;
    url: string;
    logoImage: {
      url: {
        fullSize?: string;
        thumbnail?: string;
      };
    };
  }[];
}

export interface LayoutElementContentConfigurationTopTenTags extends LayoutElementContentConfiguration {
  tags: Tag[];
}

export interface LayoutElementContentConfigurationVideoEx extends LayoutElementContentConfiguration {
  htmlContent: { desktop?: string; mobile?: string };
  title: string;
  tag: string;
  url: string;
}

export interface LayoutElementContentConfigurationBlockTitleWithTags extends LayoutElementContentConfiguration {
  selectedTags: Tag[];
  title: string;
}
