import { FocusPointUrlWithAspectRatio } from './focus-point.definitions';

export interface SecretDaysCalendar {
  backgroundImage?: SecretDaysCalendarImage;
  colorOfDays?: string;
  days?: CalendarDay[];
  id: string;
  name?: string;
  numberOfDays?: number;
  sponsorshipForHeader: SecretDaysCalendarSponsor;
  sponsorshipForFooter: SecretDaysCalendarSponsor;
  sponsorshipMainOnLeft: SecretDaysCalendarSponsor;
  isSponsorHeaderActive?: boolean;
  isSponsorFooterActive?: boolean;
  isSponsorLeftActive?: boolean;
  layerImageOfDays?: SecretDaysCalendarImage;
  defaultDayBackgroundBeforeOpenImage?: SecretDaysCalendarImage;
  defaultDayBackgroundAfterOpenImage?: SecretDaysCalendarImage;
}

export interface CalendarDay {
  id: string;
  body?: any[];
  adoceanId?: string;
  backgroundAfterOpenImage?: SecretDaysCalendarImage;
  backgroundBeforeOpenImage?: SecretDaysCalendarImage;
  name?: string;
  openableEnd?: string;
  openableStart?: string;
  sponsorshipHeader: SecretDaysCalendarSponsor;
  sponsorshipFooter: SecretDaysCalendarSponsor;
  sponsorshipDayOnLeft?: SecretDaysCalendarSponsor;
  isSponsorHeaderActive?: boolean;
  isSponsorFooterActive?: boolean;
  isSponsorLeftActive?: boolean;
  visibilityEnd?: string;
  visibilityStart?: string;
  backgroundImageUrl?: string;
  isOpenable?: boolean;
}

export interface SecretDaysCalendarImage {
  altText?: string;
  caption?: string;
  fullSizeUrl: string;
  thumbnailUrl: string;
  title?: string;
  source?: string;
  focusedImages?: FocusPointUrlWithAspectRatio[];
  photographer?: string;
}

export interface SecretDaysCalendarSponsor {
  title?: string;
  url?: string;
  fontColor?: string;
  imageHeight?: string;
  imageWidth?: string;
  slogan?: string;
  logo?: SecretDaysCalendarImage;
  highlightedColor?: string;
  isActive?: boolean;
  endDate?: {
    date?: string;
    timezone?: string;
  };
  startDate?: {
    date?: string;
    timezone?: string;
  };
}
