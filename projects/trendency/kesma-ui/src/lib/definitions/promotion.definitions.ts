import { ArticleBody } from './article-card.definitions';
import { FocusPointUrlWithAspectRatio } from './focus-point.definitions';

export interface Promotion {
  id: string;
  title: string;
  lead: string;
  badge: string;
  description: ArticleBody[];
  image?: string;
  imageThumbnail?: string;
  imageFocusedImages?: FocusPointUrlWithAspectRatio;
  imageThumbnailFocusedImages?: FocusPointUrlWithAspectRatio;
  dateStart: string;
  dateEnd: string;
  isActive: boolean;
  isDeleted: boolean;
}
