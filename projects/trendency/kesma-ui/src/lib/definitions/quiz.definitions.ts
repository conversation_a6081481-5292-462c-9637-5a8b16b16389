import { FakeBool } from './common.definitions';
import { FocusPointUrlWithAspectRatio } from './focus-point.definitions';

export type Quiz = Readonly<{
  id: string;
  title: string;
  isAdOceanEnabled?: boolean;
  isMoreInfoLinkEnabled?: boolean;
  moreInfoLink?: string;
  adOceanId?: string;
  questions: QuizQuestion[];
  ratings: QuizRating[];
  startDate?: string;
  endDate?: string;
  quizCategoryPageUrl?: string;
}>;

export type QuizQuestion = Readonly<{
  id: string;
  title: string;
  image?: string | { thumbnailUrl?: string };
  thumbnailUrl?: string; // Only in case of Layout element in `data`
  //imageFocusedImages?: FocusPointUrlWithAspectRatio;
  answers: QuizAnswer[];
}>;

export type QuizQuestionImage = Readonly<{
  focusedImages: FocusPointUrlWithAspectRatio;
  fullSizeUrl: string;
  variantId: number;
  id: string;
}>;

export type QuizRating = Readonly<{
  id: string;
  ratingFrom: string;
  ratingTo: string;
  text: string;
  thumbnailUrl: string;
  //imageFocusedImages?: FocusPointUrlWithAspectRatio;
  image?: string;
  isFallbackValue?: boolean;
}>;

export type QuizAnswer = Readonly<{
  id: string;
  title: string;
  order: string;
  isCorrect: FakeBool | boolean; // FakeBool arrives only in case of Layout element in `data`,
  explanation?: string;
  image?: string; // Image URL for the answer
}>;
