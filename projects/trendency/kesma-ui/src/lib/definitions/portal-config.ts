export type ConfigSettingValue = '1' | '';

export enum PortalConfigSetting {
  CONTENT_ARTICLE_CHARACTER_STATISTICS_MUST_CONTAIN_TITLE = 'content_article_character_statistics_must_contain_title',
  CONTENT_ARTICLE_CHARACTER_STATISTICS_MUST_CONTAIN_LEAD = 'content_article_character_statistics_must_contain_lead',
  CONTENT_ARTICLE_RESTRICT_RECOMMENDED_TITLE_AND_USE_AS_SLUG = 'content_article_restrict_recommended_title_and_use_as_slug',
  LAYOUT_OPTION_EXCLUDE_LINE_UNDER_ARTICLE = 'layout_option_exclude_line_under_article',
  SLUG_GENERATION_ON_CHANGE_FOR_ARTICLES = 'slug_generation_on_change_for_articles',
  GALLERY_TAGS_AVAILABLE = 'gallery_tags_available',
  GALLERY_DOSSIERS_AVAILABLE = 'gallery_dossiers_available',
  GALLERY_COLUMNS_AVAILABLE = 'gallery_columns_available',
  GALLERY_SPORTS_AVAILABLE = 'gallery_sports_available',
  GALLERY_PHOTOGRAPHER_AVAILABLE = 'gallery_photographer_available',
  GALLERY_PRE_TITLE_AVAILABLE = 'gallery_pre_title_available',
  GALLERY_RECOMMENDATION_STRUCTURE_SAME_WITH_GALLERY_DETAILS = 'gallery_recommendation_structure_same_with_gallery_details',
  GALLERY_REGIONS_AVAILABLE = 'gallery_regions_available',
  GALLERY_SELECTABLE_ROBOTS_TAG_ENABLED = 'gallery_selectable_robots_tag_enabled',
  ARTICLE_DOSSIER_MANY_TO_MANY_CONNECTION_ENABLED = 'article_dossier_many_to_many_connection_enabled',
  CONTENT_ARTICLE_PRINT_STATUS_DIFFERENT_LABEL_BORS = 'content_article_print_status_different_label_bors',
  ENABLE_COLOR_PICKER_IN_LAYOUT = 'enable_color_picker_in_layout',
  CONTENT_ARTICLE_SHORT_NEWS_TYPE_ENABLED = 'content_article_short_news_type_enabled',
  CONTENT_ARTICLE_FAST_NEWS_TYPE_ENABLED = 'content_article_fast_news_type_enabled',
  SHOW_PRINT_SUBTITLE_IN_ONLINE_ARTICLES = 'show_print_subtitle_in_online_articles',
  TAG_ON_HEADER_BAR_ENABLED = 'tag_on_header_bar_enabled',
  CONTENT_ARTICLE_PRINT_CHAR_COUNT_IMAGE_SUB = 'content_article_print_char_count_image_sub',
  CONTENT_ARTICLE_ONLINE_CHAR_COUNT_IMAGE_SUB = 'content_article_online_char_count_image_sub',
  SELECT_PARENT_COLUMN_IN_ARTICLE_EDITOR_WHEN_CHILD_COLUMN_SELECTED = 'select_parent_column_in_article_editor_when_child_column_is_selected',
  CONTENT_ARTICLE_STATISTICS_NUMBER_OF_CHARACTERS_IN_ONE_ROW_60 = 'content_article_statistics_number_of_characters_in_one_row_60',
  CONTENT_ARTICLE_STATISTICS_NUMBER_OF_CHARACTERS_IN_ONE_ROW_30 = 'content_article_statistics_number_of_characters_in_one_row_30',
  CMS_MENU_TYPE_SPORTS = 'cms_menu_type_sports',
  CMS_IS_CENTRAL_PUBLISHING_ENABLED = 'cms_is_central_publishing_enabled',
  CMS_MENU_TYPE_SPONSORSHIPS = 'cms_menu_type_sponsorships',
  CMS_MENU_TYPE_LAYOUT_COLUMN = 'cms_menu_type_layout_column',
  CMS_MENU_TYPE_TAGS = 'cms_menu_type_tags',
  CMS_MENU_TYPE_REGIONS = 'cms_menu_type_regions',
  ENABLE_FOUNDATION_CONTENTS = 'enable_foundation_contents',
  MENU_MORE_HEADER_CONFIG_CHECKBOX_ENABLED = 'menu_more_header_config_checkbox_enabled',
  ENABLE_DYNAMIC_CONTENT_TAGGER = 'enable_dynamic_content_tagger',
  ENABLE_EXTERNAL_CONTRIBUTOR_FEATURE = 'enable_external_contributor_feature',
  CONTENT_ARTICLE_PRINT_STATUS_DIFFERENT_LABEL_MANDINER = 'content_article_print_status_different_label_mandiner',
  CONTENT_ARTICLE_PR_TAG_ENABLED = 'content_article_pr_tag_enabled',
  CONTENT_DOSSIER_NEWS_FEED = 'content_dossier_news_feed',
  ENABLE_TO_SHOW_PRINT_ONLY_COLUMNS_FOR_PRINT_ARTICLES = 'enable_to_show_print_only_columns_for_print_articles',
  CONTENT_ARTICLE_REVIEWABLE_CHECKBOX = 'content_article_reviewable_checkbox',
  CONTENT_ARTICLE_MINUTE_TO_MINUTE_BUZZWORD = 'content_article_minute_to_minute_buzzword',
  SLUG_GENERATION_FROM_SEO_TITLE_FOR_ARTICLES = 'slug_generation_from_seo_title_for_articles',
  CONTENT_ARTICLE_GLOSSARY = 'content_article_glossary',
  ENABLE_HIGHLIGHTED_MENU_ITEMS = 'enable_highlighted_menu_items',
  ENABLE_EXTERNAL_PUBLIC_AUTHOR_SLUG = 'enable_external_public_author_slug',
  ENABLE_TELEKOM_VIVICITTA = 'enable_telekom_vivicitta',
  ENABLE_ELECTIONS_2024 = 'enable_elections_2024',
  ENABLE_ELECTIONS_2024_RESULTS = 'enable_elections_2024_results',
  ENABLE_FOOTBALL_EB_ELEMENTS = 'enable_football_eb_elements',
  MENU_MORE_HEADER_CONFIG_CHECKBOX_JOURNAL_ISSUE = 'menu_more_header_config_checkbox_journal_issue',
  MENU_TYPE_AVAILABLE_SPORT_SINGLE_ELIMINATION = 'menu_type_available_sport_single_elimination',
  ENABLE_OLYMPICS_ELEMENTS = 'enable_olympics_elements',
  ENABLE_OLYMPICS_LAYOUT_COMPONENTS = 'enable_olympics_layout_components',
  SHOW_NUMBER_OF_ARTICLES_BY_AUTHORS_ENABLED = 'show_number_of_articles_by_authors_enabled',
  ENABLE_EXTERNAL_PUBLIC_AUTHOR_M2M = 'enable_external_public_author_m2m',
  ENABLE_BAYER_ZSOLT_BLOG_COMPONENTS = 'enable_bayer_zsolt_blog_components',
  MENU_TYPE_GASTRO_EXPERIENCE = 'menu_type_gastro_experience',
  PORTAL_USER_ENABLE_REMEMBER_ME = 'portal_user_enable_remember_me',
  ENABLE_SPONSORED_ARTICLE_RECOMMENDATION = 'enable_sponsored_article_recommendation',
  ENABLE_SEND_RECIPE_TO_NATIONAL_DISH_2025 = 'enable_send_recipe_to_national_dish_2025',
}

export type PortalConfigData = Readonly<Record<PortalConfigSetting, ConfigSettingValue>>;
