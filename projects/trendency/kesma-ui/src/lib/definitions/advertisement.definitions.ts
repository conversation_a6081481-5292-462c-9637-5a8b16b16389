export type Advertisement = Readonly<{
  zonaId: string;
  bannerName: AdvertisementBannerName;
  medium: AdvertisementMedium;
  pageType: string;
  masterId: string;
  priority?: number;
  isAdultAd?: boolean;
}>;

export interface AdvertsMeta {
  keys?: string;
  vars: string;
}

export interface SlaveConfig {
  myMaster?: string;
  onServerEmissionEmpty?: () => void;
  onServerEmission?: () => void;
  onLoad?: () => void;
  preview?: boolean;
}

export type AdvertisementMedium = 'desktop' | 'mobile' | 'all' | 'tablet';

export enum SecondaryFilterAdvertType {
  FALLBACK = 'fallback',
  REPLACEABLE = 'replaceable',
  REPLACEABLE_WITH_ABSOLUTE = 'with-absolute',
  NONE = 'none',
}

export type AdvertisementBannerName =
  | 'interstitial'
  | 'layer'
  | 'leaderboard_1'
  | 'leaderboard_2'
  | 'leaderboard_3'
  | 'leaderboard_4'
  | 'leaderboard_5'
  | 'leaderboard_6'
  | 'leaderboard_7'
  | 'leaderboard_8'
  | 'leaderboard_9'
  | 'leaderboard_10'
  | 'box_1'
  | 'box_2'
  | 'box_3'
  | 'box_4'
  | 'box_5'
  | 'box_6'
  | 'box_7'
  | 'box_8'
  | 'box_9'
  | 'box_10'
  | 'roadblock_1'
  | 'roadblock_2'
  | 'roadblock_3'
  | 'roadblock_4'
  | 'roadblock_5'
  | 'roadblock_6'
  | 'roadblock_7'
  | 'roadblock_8'
  | 'roadblock_9'
  | 'roadblock_10'
  | 'pr_box'
  | 'billboard'
  | 'footer_ad'
  | 'games_page'
  | 'mobilrectangle_1'
  | 'mobilrectangle_2'
  | 'mobilrectangle_3'
  | 'mobilrectangle_4'
  | 'mobilrectangle_5'
  | 'mobilrectangle_6'
  | 'mobilrectangle_7'
  | 'mobilrectangle_8'
  | 'mobilrectangle_9'
  | 'mobilrectangle_10'
  | 'ecomm_1'
  | 'ecomm_2'
  | 'ecomm_3'
  | 'ecomm_4'
  | 'ecomm_5'
  | 'ecomm_6'
  | 'googlenativ_1'
  | 'googlenativ_2'
  | 'googlenativ_3'
  | 'technikai_1'
  | 'technikai_2'
  | 'technikai_3'
  | 'technikai_4'
  | 'technikai_5'
  | 'mobilinterrupter_1'
  | 'mobilinterrupter_2'
  | 'mobilinterrupter_3'
  | 'mobilinterrupter_4'
  | 'mobilinterrupter_5'
  | 'desktopinterrupter_1'
  | 'desktopinterrupter_2'
  | 'desktopinterrupter_3'
  | 'desktopinterrupter_4'
  | 'desktopinterrupter_5'
  | 'mobilbottom'
  | 'mobiltop'
  | 'szponzorcsik'
  | 'mobilrectangle_footer'
  | 'leaderboard_footer'
  | 'recommendation_1'
  | 'kepes-ajanlo_1'
  | 'kepes-ajanlo_2'
  | 'kepes-ajanlo_3'
  | 'prcikkfix_1'
  | 'prcikkfix_2'
  | 'prcikkfix_3'
  | 'prcikkfix_4'
  | 'prcikkfix_5'
  | 'roadblock_ottboxextra'
  | 'mobilrectangle_ottboxextra'
  | 'szponzorcsik_also'
  | 'szponzorcsik_felso'
  | 'leaderboard_1_bottom'
  | 'leaderboard_1_middle'
  | 'leaderboard_1_top'
  | 'leaderboard_2_bottom'
  | 'leaderboard_2_middle'
  | 'leaderboard_2_top'
  | 'leaderboard_3_bottom'
  | 'leaderboard_3_middle'
  | 'leaderboard_3_top';

export type AdvertisementsByMedium = Readonly<{
  desktop: {
    [bannerName in AdvertisementBannerName]: Advertisement;
  };
  mobile: {
    [bannerName in AdvertisementBannerName]: Advertisement;
  };
}>;

export type ArticleAdvertisements = Record<'desktop' | 'mobile', Record<string | AdvertisementBannerName, Advertisement>>;

export type AdvertisementVariablesByMediums = Readonly<{
  desktop?: Advertisement;
  mobile?: Advertisement;
}>;
