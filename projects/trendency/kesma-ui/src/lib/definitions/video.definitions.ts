import { Tag } from './article-card.definitions';
import { VideoCard } from './video-card.definitions';
import { FocusPointUrlWithAspectRatio } from './focus-point.definitions';

export type Video = Readonly<{
  columnId?: string;
  columnSlug?: string;
  columnTitle?: string;
  description?: string;
  id: string;
  isRecommended?: string;
  lead?: string;
  length?: string;
  similarVideos: VideoCard[];
  slug: string;
  sponsorshipId?: string;
  sponsorshipTitle?: string;
  tags: Tag[];
  thumbnail?: string;
  thumbnailCreatedAt?: string;
  title: string;
  videaUrl: string;
  publishDate?: string | Date;
}>;

/** Used with /v2/media/video endpoint */
export type BackendVideo = Readonly<{
  id: string;
  title: string;
  slug: string;
  lead?: string;
  /* In seconds */
  length?: number;
  isShort: boolean;
  isRecommended: boolean;
  publicDate: string;
  column?: {
    id: string;
    title: string;
    slug: string;
  };
  /** High res */
  coverImageUrl?: string;
  coverImage?: Readonly<{
    /** Small res */
    thumbnailUrl?: string;
    focusedImages?: FocusPointUrlWithAspectRatio;
  }>;
  videaUrl: string;
}>;

export interface VideoListParams {
  page?: number;
  rowCount?: number;
  isShort?: boolean;
}
