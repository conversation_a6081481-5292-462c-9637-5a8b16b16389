import { RecipeDifficulty } from './recipe.definitions';

export enum RelatedType {
  COLUMN = 'Column',
  GALLERY_COLLECTION = 'GalleryCollection',
  INSTITUTION_COLLECTION = 'InstitutionCollection',
  TUNDERSZEPEK_PAGE = 'TunderszepekPage',
  CUSTOM_URL = 'CustomUrl',
  STATIC_PAGE = 'StaticPage',
  DROPDOWN = 'Dropdown',
  ARTICLE = 'Article',
  HOME_PAGE = 'HomePage',
  SEARCH_PAGE = 'SearchPage',
  WEATHER_PAGE = 'WeatherPage',
  OPINION_ARTICLE = 'OpinionArticle',
  OPINION_PAGE = 'OpinionPage',
  CUSTOM_BUILT_PAGE = 'CustomBuiltPage',
  DOSSIER = 'Dossier',
  DOSSIER_COLLECTION = 'DossierCollection',
  REGION_PAGE = 'Region',
  JOURNAL_ISSUE = 'JournalIssue',
  JOURNAL_ISSUE_COLLECTION = 'JournalIssueCollection',
  ARTICLE_COLLECTION = 'ArticleCollection',
  VIDEO_COLLECTION = 'VideoCollection',
  VIDEO = 'Video',
  AUTHOR = 'Author',
  COMPETITION = 'Competition',
  COMPETITION_TEAM = 'CompetitionTeam',
  HIDDEN_PAGE = 'HiddenPage',
  RECIPE = 'Recipe',
  FULL_RECIPE = 'FullRecipe',
  NSO_TV = 'NsoTv',
  TWENTY_FOUR_HOURS = 'TwentyFourHours',
  PODCAST = 'Podcast',
  PODCAST_COLLECTION = 'PodcastCollection',
  REGION = 'Region',
  ADS_PAGE = 'AdsPage',
  GRIEF_PAGE = 'GriefPage',
  NOTE = 'NotebookArticle',
  PROGRAM_RECOMMENDATION = 'ProgramRecommendation',
  PROGRAM_RECOMMENDATION_COLLECTION = 'ProgramRecommendationCollection',
}

export type RelatedArticle = Readonly<{
  id: string;
  title: string;
  slug: string;
  language?: string | null;
  columnSlug?: string;
  publishDate?: { date: string; timezone_type: number; timezone: string };
  // FullArticle optional properties
  coverImage?: {
    fullSizeUrl?: string;
    thumbnailUrl?: string;
  };
  hasGallery?: boolean;
  isAdultsOnly?: boolean;
  minuteToMinute?: string; // Fakebool
  videoType?: boolean;
  columnTitle?: string;
  // FullRecipe optional properties
  containsVideo?: boolean;
  coverImageUrls?: {
    fullSizeUrl?: string;
    thumbnailUrl?: string;
  };
  difficultyTranslated?: {
    key?: string;
    title?: string;
  };
  mmeWarranty?: boolean;
  publicAuthorData?: {
    avatar?: {
      fullSizeUrl?: string;
      thumbnailUrl?: string;
    };
    fullName?: string;
    id?: string;
    isMaestroAuthor?: boolean;
    slug?: string;
  };
  sentByUserData?: {
    id?: string;
    fullName?: string;
    selectedImage?: string;
    avatar?: string;
    avatarColor?: string;
  };
  competition?: string;
  team?: string;
}>;
