<div class="box-container">
  <div class="header">
    <a *ngIf="logoHref; else logoTemplate" [href]="logoHref" target="_blank">
      <ng-container *ngTemplateOutlet="logoTemplate"></ng-container>
    </a>
    <div class="header-buttons">
      <ul *ngIf="columnButtons" class="column-buttons">
        <li *ngFor="let button of columnButtons" class="column-button">
          <a
            [href]="button.url"
            target="_blank"
            class="button-link"
            [style.background-color]="columnButtonBackgroundColor"
            [style.color]="columnButtonTextColor"
            >{{ button.label }}</a
          >
        </li>
      </ul>
    </div>
    <hr class="header-bottom-divider" />
  </div>
  <div *ngIf="articles" class="big">
    <div class="main-article-container">
      <ng-container *ngTemplateOutlet="mainArticle; context: { $implicit: articles[0] }"></ng-container>
    </div>
    <div class="main-article-small">
      <ng-container *ngTemplateOutlet="smallArticle; context: { $implicit: articles[0], showLead: true }"></ng-container>
      <hr class="article-divider" [style.border-top-color]="dividerColor" />
    </div>
  </div>
  <div *ngIf="articles" class="small1">
    <ng-container *ngTemplateOutlet="smallArticleList; context: { $implicit: articles | slice: 1 : 4 }"></ng-container>
  </div>
  <div *ngIf="articles" class="small2">
    <ng-container *ngTemplateOutlet="smallArticleList; context: { $implicit: articles | slice: 4 : 7 }"></ng-container>
  </div>
  <div *ngIf="articles && articles.length > 7" class="small-additional" [style.border-top-color]="dividerColor">
    <div class="additional-article" *ngFor="let article of articles | slice: 7; let index = index; let last = last">
      <hr *ngIf="index > 3" class="article-divider small-divider" [style.border-top-color]="dividerColor" />
      <hr *ngIf="index > 0 && index <= 3" class="article-divider small-divider only-small" [style.border-top-color]="dividerColor" />
      <ng-container *ngTemplateOutlet="smallArticle; context: { $implicit: article, skipImage: true }"></ng-container>
      <hr *ngIf="last" class="article-divider small-divider only-small" [style.border-top-color]="dividerColor" />
    </div>
  </div>
</div>
<ng-template #mainArticle let-article>
  <div *ngIf="article" class="main-article">
    <div class="image-container">
      <a *ngIf="article.imageUrl" [href]="article.url" target="_blank" aria-label="Cikk kép"
        ><img [src]="article.imageUrl" alt="" class="image" loading="lazy"
      /></a>
      <a class="title-container" [href]="article.url" target="_blank">
        <span
          class="title"
          [style.background-color]="columnButtonBackgroundColor"
          [style.color]="columnButtonTextColor"
          [style.box-shadow]="'-5px 0px 0 4px ' + columnButtonBackgroundColor + ', 5px 0px 0 4px ' + columnButtonBackgroundColor"
          >{{ article.title }}</span
        >
      </a>
    </div>
    <p *ngIf="article.lead" class="lead" [style.color]="leadTextColor">{{ article.lead }}</p>
  </div>
</ng-template>
<ng-template #smallArticle let-article let-skipImage="skipImage" let-showLead="showLead">
  <div class="small-article" *ngIf="article">
    <a *ngIf="article.imageUrl && !skipImage" [href]="article.url" target="_blank"
      ><img [src]="article.imageUrl" class="image" loading="lazy" [alt]="article.title"
    /></a>
    <a [href]="article.url" target="_blank" class="title" [style.color]="articleTitleTextColor">{{ article.title }}</a>
    <p *ngIf="showLead && article.lead" class="lead" [style.color]="leadTextColor">{{ article.lead }}</p>
  </div>
</ng-template>
<ng-template #smallArticleList let-articles>
  <ng-container *ngFor="let article of articles; let index = index; let last = last">
    <ng-container *ngTemplateOutlet="smallArticle; context: { $implicit: article, skipImage: index > 0 }"></ng-container>
    <hr *ngIf="!last" class="article-divider" [style.border-top-color]="dividerColor" />
    <hr *ngIf="last" class="article-divider only-small" [style.border-top-color]="dividerColor" />
  </ng-container>
</ng-template>
<ng-template #logoTemplate>
  <img *ngIf="logo" [src]="logo" [alt]="title || ''" [title]="title || ''" class="header-logo" loading="lazy" />
</ng-template>
