<div class="pager">
  <div class="buttons">
    @if (navType === 'event') {
      <button *ngIf="hasFirstLastButton" class="button-arrow first" aria-label="Első oldal" (click)="onFirstPageClick()" [ngClass]="{ disabled: isFirst }">
        <i class="icon icon-first"></i>
      </button>
      <button class="button-arrow prev" aria-label="Előző" [ngClass]="{ disabled: isFirst }" (click)="onPrevClick()">
        <i class="icon icon-prev"></i>
      </button>
    } @else {
      <a
        [routerLink]="[baseRoute]"
        [queryParams]="firstPage"
        [fragment]="baseFragment"
        *ngIf="hasFirstLastButton"
        class="button-arrow first"
        aria-label="Első oldal"
        [ngClass]="{ disabled: isFirst }"
      >
        <i class="icon icon-first"></i>
      </a>
      <a
        *ngIf="!removeDisabledElement || (removeDisabledElement && !isFirst)"
        [routerLink]="[baseRoute]"
        [queryParams]="prevPage"
        [fragment]="baseFragment"
        class="button-arrow prev"
        aria-label="Előző"
        [ngClass]="{ disabled: isFirst }"
      >
        <i class="icon icon-prev"></i>
      </a>
    }
  </div>

  <ng-container *ngIf="hasSkipButton && isOpeningEllipses">
    <ng-container *ngIf="showFirstPage">
      @if (navType === 'event') {
        <button class="button-num" (click)="onFirstPageClick()" [ngClass]="{ active: isFirst }">1</button>
      } @else {
        <a [routerLink]="[baseRoute]" [queryParams]="firstPage" [fragment]="baseFragment" class="button-num" [class.active]="isFirst">1</a>
      }
    </ng-container>
    @if (!showFirstPage || pages[0] > maxDisplayedPages - 1) {
      @if (navType === 'event') {
        <button class="button-num more" (click)="onSkipLeftClick()">...</button>
      } @else {
        <a [routerLink]="baseRoute" [queryParams]="skipLeft" [fragment]="baseFragment" class="button-num more">...</a>
      }
    }
  </ng-container>

  <div *ngIf="isListPager" class="list-pager nums">
    @for (page of pages; track page; let i = $index) {
      @if (navType === 'event') {
        <button class="button-num" (click)="onPageClick(page)" [ngClass]="{ active: page === currentPage }">
          {{ page }}
        </button>
      } @else {
        <a [routerLink]="baseRoute" [queryParams]="listParams[i]" [fragment]="baseFragment" class="button-num" [ngClass]="{ active: page === currentPage }">
          {{ page }}
        </a>
      }
    }
  </div>

  <div *ngIf="isCountPager" class="count-pager nums">
    <p class="count-number">
      {{ currentPage }}
    </p>
    <ng-container *ngIf="!showTotalPagesPositionAtRight">
      <div class="separator">/</div>
      <p class="count-number">
        {{ totalPages }}
      </p>
    </ng-container>
  </div>

  <ng-container *ngIf="hasSkipButton && isClosingEllipses">
    @if (!showLastPage || pages[pages.length - 1] + 1 !== totalPages) {
      @if (navType === 'event') {
        <button class="button-num more" (click)="onSkipRightClick()">...</button>
      } @else {
        <a [routerLink]="baseRoute" [queryParams]="skipRight" [fragment]="baseFragment" class="button-num more">...</a>
      }
    }
    <ng-container *ngIf="showLastPage">
      @if (navType === 'event') {
        <button class="button-num" (click)="onLastPageClick()" [ngClass]="{ active: totalPages === currentPage }">
          {{ totalPages }}
        </button>
      } @else {
        <a [routerLink]="[baseRoute]" [queryParams]="lastPage" [fragment]="baseFragment" class="button-num" [ngClass]="{ active: totalPages === currentPage }">
          {{ totalPages }}
        </a>
      }
    </ng-container>
  </ng-container>

  <div class="buttons">
    @if (navType === 'event') {
      <button class="button-arrow next" aria-label="Következő" [ngClass]="{ disabled: isLast }" (click)="onNextClick()">
        <i class="icon icon-next"></i>
      </button>
      <button *ngIf="hasFirstLastButton" class="button-arrow last" aria-label="Utolsó oldal" (click)="onLastPageClick()" [ngClass]="{ disabled: isLast }">
        <i class="icon icon-last"></i>
      </button>
    } @else {
      <a
        *ngIf="!removeDisabledElement || (removeDisabledElement && !isLast)"
        [routerLink]="isLast ? null : [baseRoute]"
        [queryParams]="nextPage"
        [fragment]="baseFragment"
        class="button-arrow next"
        aria-label="Következő"
        [ngClass]="{ disabled: isLast }"
      >
        <i class="icon icon-next"></i>
      </a>
      <a
        [routerLink]="[baseRoute]"
        [queryParams]="lastPage"
        [fragment]="baseFragment"
        *ngIf="hasFirstLastButton"
        class="button-arrow last"
        aria-label="Utolsó oldal"
        [ngClass]="{ disabled: isLast }"
      >
        <i class="icon icon-last"></i>
      </a>
    }
  </div>

  <ng-container *ngIf="showTotalPagesPositionAtRight">
    <div class="separator">/</div>
    <p class="count-number">
      {{ totalPages }}
    </p>
  </ng-container>
</div>
