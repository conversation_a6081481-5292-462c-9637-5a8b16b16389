<div class="mwg_footer_top">
  <div class="container outer_table">
    <div class="inner_table mw_logo">
      <a href="https://mediaworks.hu/" target="_blank">
        <img alt="Mediaworks Content First" height="46" loading="lazy" src="/assets/images/mw/mw-logo-2024-red-blue.svg" width="207" />
      </a>
    </div>
    <div class="inner_table mw_text">
      Portfóliónk minőségi tartalmat jelent minden olvasó számára. Egyedülálló elérést, országos lefedettséget és változatos megjelenési lehetőséget biztosít.
      Folyamatosan keressük az új irányokat és fejlődési lehetőségeket. Ez jövőnk záloga.
    </div>
    <div class="inner_table mw_social">
      <a class="icon_fb extra_margin" href="https://www.facebook.com/mediaworkshungary" target="_blank" aria-label="facebook">&nbsp;</a>
      <a class="icon_in" href="https://www.linkedin.com/company/mediaworks-hungary-zrt-" target="_blank" aria-label="linkedin">&nbsp;</a>
    </div>
  </div>
</div>
<div class="mwg_footer_lower">
  <div class="domainFrame">
    <ng-container *ngFor="let link of data">
      <ng-container [ngSwitch]="link?.name">
        <div *ngSwitchCase="'Regionális hírportálok'" class="domainTitle">
          {{ link?.name }}
        </div>
        <div *ngSwitchCase="'Közélet'" class="domainTitle">
          {{ link?.name }}
        </div>
        <div *ngSwitchCase="'Sport'" class="domainTitle">
          {{ link?.name }}
        </div>
        <div *ngSwitchCase="'Gazdaság'" class="domainTitle">
          {{ link?.name }}
        </div>
        <div *ngSwitchCase="'Magazin'" class="domainTitle">
          {{ link?.name }}
        </div>
        <div *ngSwitchCase="'Bulvár'" class="domainTitle">
          {{ link?.name }}
        </div>
        <div *ngSwitchCase="'Szolgáltatás'" class="domainTitle">
          {{ link?.name }}
        </div>
        <div *ngSwitchCase="'Rádió'" class="domainTitle">
          {{ link?.name }}
        </div>
        <a *ngSwitchDefault [href]="link?.url" class="externalPageLink">
          {{ link?.name }}
        </a>
      </ng-container>
    </ng-container>
  </div>
</div>
