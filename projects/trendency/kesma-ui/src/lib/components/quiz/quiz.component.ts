import { ChangeDetectionStrategy, ChangeDetectorRef, Component, HostBinding, inject, OnInit } from '@angular/core';
import { Quiz, QuizQuestion, QuizRating } from '../../definitions';
import { BaseComponent } from '../base/base.component';
import { Ng<PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';

@Component({
  selector: 'kesma-quiz',
  templateUrl: './quiz.component.html',
  styleUrls: ['./quiz.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgFor, NgClass, NgIf],
})
export class QuizComponent extends BaseComponent<Quiz> implements OnInit {
  @HostBinding('class.quiz') inLayout = true;
  @HostBinding('class') hostClass = 'quiz';

  public correctCount = 0;
  public answeredCount = 0;
  public givenAnswers: number[] = [];
  public rating: QuizRating | undefined | null;
  public currentQuestionNumber = 0;
  public currentQuestion: QuizQuestion | undefined | null;
  activatedElement = '';
  goodAnswers = 0;

  protected cdr = inject(ChangeDetectorRef);

  get placeholderImage() {
    return '../../../../assets/images/ripost-placeholder.jpg';
  }

  override ngOnInit() {
    super.ngOnInit();
    this.currentQuestion = this.data?.questions?.[0];
  }

  selectAnswer(id: string) {
    this.activatedElement = id;
  }

  onSelectAnswer(question: number, answer: number) {
    this.givenAnswers[question] = answer;

    this.checkFinished();
  }

  public onFBShareClick() {
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${window.location.href}`, 'pop', 'width=600, height=400, scrollbars=no');
  }

  checkFinished() {
    if (this.data) {
      this.correctCount = this.givenAnswers.filter((answer, questionIndex) => this.data?.questions[questionIndex].answers[answer].isCorrect).length;
      this.answeredCount = this.givenAnswers.filter((answerIndex) => answerIndex !== undefined).length;
      if (this.answeredCount === this.data.questions.length) {
        this.getResult(this.correctCount);
      }
    }
  }

  private getResult(correctCount: number) {
    const fallbackData = {
      id: '',
      thumbnailUrl: this.placeholderImage,
      text: `${this.correctCount}/${this.answeredCount}`,
      ratingFrom: '0',
      ratingTo: '1',
      isFallbackValue: true,
    };

    this.rating = this.data?.ratings.find((rating) => Number(rating?.ratingFrom) <= correctCount && Number(rating?.ratingTo) >= correctCount) ?? fallbackData;
  }
}
