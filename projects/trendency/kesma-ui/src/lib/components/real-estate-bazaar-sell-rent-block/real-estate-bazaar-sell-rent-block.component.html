@if (realEstateBazaar().length > 0) {
  <section class="real-estate">
    <div class="wrapper">
      <div class="real-estate-wrapper">
        <div class="real-estate-header">
          <img trImageLazyLoad class="real-estate-logo" src="./assets/images/ingatlanbazar-header.svg" alt="Ingatlanbazár" />
          <a href="https://www.ingatlanbazar.hu/" target="_blank" class="real-estate-more"
            >Még több ingatlan <kesma-icon class="real-estate-more-icon" name="foreign-link"
          /></a>
        </div>
        <ul class="real-estate-tabs">
          <li class="real-estate-tabs-item">
            <button class="real-estate-tabs-button" [class.is-active]="activeTabIsForSale()" (click)="handleTabClick('forSale')">Eladó</button>
          </li>
          <li class="real-estate-tabs-item">
            <button class="real-estate-tabs-button" [class.is-active]="!activeTabIsForSale()" (click)="handleTabClick('forRent')">Kiad<PERSON></button>
          </li>
        </ul>
        <div class="real-estate-body">
          <div class="real-estate-item-container">
            @for (realEstate of filteredBazaar(); track $index) {
              <div class="real-estate-item">
                <kesma-real-estate-bazaar-sell-rent-block-card [data]="realEstate"></kesma-real-estate-bazaar-sell-rent-block-card>
              </div>
            }
          </div>
        </div>
        <div class="real-estate-more-wrapper">
          <a href="https://www.ingatlanbazar.hu/" target="_blank" class="real-estate-more"
            >Még több ingatlan<kesma-icon class="real-estate-more-icon" name="foreign-link"
          /></a>
        </div>
      </div>
    </div>
  </section>
}
