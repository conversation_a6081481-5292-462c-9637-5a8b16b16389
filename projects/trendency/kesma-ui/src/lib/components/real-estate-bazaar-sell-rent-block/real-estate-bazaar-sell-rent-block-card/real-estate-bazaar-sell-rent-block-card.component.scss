@use '../../../../scss/shared' as *;

.real-estate-card {
  display: flex;
  flex-direction: column;
  color: var(--ingatlanbazar-red-400);
  margin-bottom: 20px;

  @include media-breakpoint-down(sm) {
    max-width: 200px;
  }

  .real-estate-card-image {
    height: 143px;
    background-color: black;
    position: relative;

    img {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .real-estate-card-info-box {
    background-color: var(--ingatlanbazar-gray-300);
    min-height: 143px;
    padding: 15px;
  }

  .real-estate-info-title {
    font-size: 14px;
    font-weight: 600;
    line-height: 18px;
    letter-spacing: 0.14px;
  }

  .real-estate-info-title,
  .real-estate-info-size {
    margin-bottom: 5px;
  }

  .real-estate-info-size,
  .real-estate-info-price {
    color: var(--ingatlanbazar-gray-400);
    font-size: 12px;
    font-weight: 600;
  }

  .real-estate-info-size {
    margin-top: 20px;
  }
}
