import { ChangeDetectionStrategy, Component, computed, inject, input } from '@angular/core';
import { RealEstateBazaarApiData } from '../../real-estate-bazaar-block/real-estate-bazaar.definitions';
import { UtilService } from '@trendency/kesma-core';

@Component({
  selector: 'kesma-real-estate-bazaar-sell-rent-block-card',
  imports: [],
  templateUrl: './real-estate-bazaar-sell-rent-block-card.component.html',
  styleUrl: './real-estate-bazaar-sell-rent-block-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RealEstateBazaarSellRentBlockCardComponent {
  data = input<RealEstateBazaarApiData>();

  private readonly utilService = inject(UtilService);

  readonly title = computed(() => {
    const title = this.data()?.title ?? '';

    if (!this.utilService.isBrowser() || !title) {
      return '';
    }

    const isMobile = window.innerWidth <= 768;
    const [maxLen, sliceLen] = isMobile ? [66, 63] : [76, 73];

    return title.length > maxLen ? `${title.slice(0, sliceLen)}...` : title;
  });
}
