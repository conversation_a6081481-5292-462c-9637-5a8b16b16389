import { ChangeDetectionStrategy, Component, computed, effect, input, signal } from '@angular/core';
import { RealEstateBazaarApiData } from '../real-estate-bazaar-block/real-estate-bazaar.definitions';
import { RealEstateBazaarSellRentBlockCardComponent } from './real-estate-bazaar-sell-rent-block-card/real-estate-bazaar-sell-rent-block-card.component';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'kesma-real-estate-bazaar-sell-rent-block',
  imports: [IconComponent, RealEstateBazaarSellRentBlockCardComponent],
  templateUrl: './real-estate-bazaar-sell-rent-block.component.html',
  styleUrl: './real-estate-bazaar-sell-rent-block.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RealEstateBazaarSellRentBlockComponent {
  realEstateBazaar = input<RealEstateBazaarApiData[]>([]);
  activeTabIsForSale = signal<boolean>(true);
  itemsInBlock = signal<number>(6);

  filteredBazaar = computed(() => {
    const items = this.realEstateBazaar();
    const suffix = this.activeTabIsForSale() ? '_elado' : '_kiado';
    return this.filterByClassPropEndingWith(items, suffix);
  });

  private filterByClassPropEndingWith(items: RealEstateBazaarApiData[], type: string): RealEstateBazaarApiData[] {
    return items
      ?.filter((item: RealEstateBazaarApiData) =>
        (
          item as RealEstateBazaarApiData & {
            class: string;
          }
        ).class.endsWith(type)
      )
      .slice(0, this.itemsInBlock());
  }

  handleTabClick(value: string): void {
    this.activeTabIsForSale.set(value === 'forSale');
  }
}
