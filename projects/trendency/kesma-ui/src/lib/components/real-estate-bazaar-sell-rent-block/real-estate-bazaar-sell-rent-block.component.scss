@use '../../../scss/shared' as *;

.real-estate {
  margin: 0;
  font-family: var(--kui-font-roboto-flex);
  container-type: inline-size;

  @include container-breakpoint-down(sm) {
    margin: 40px -15px;
  }

  .real-estate-wrapper {
    border-top: 1px solid var(--ingatlanbazar-red-500);
    border-bottom: 1px solid var(--ingatlanbazar-gray-200);
    padding: 15px 0;
    @include container-breakpoint-down(sm) {
      padding: 15px;
    }
  }

  .real-estate-header {
    display: flex;
    align-items: flex-end;
    margin-bottom: 50px;

    .real-estate-logo {
      width: 192px;
      height: 34px;
      width: auto;
    }
  }

  .real-estate-more {
    display: inline-flex;
    margin-left: auto;
    column-gap: 9px;
    font-weight: 600;
    font-size: 14px;
    line-height: 18px;
    letter-spacing: 0.14px;
    border-radius: 6px;
    height: 30px;
    color: var(--kui-white);
    justify-content: center;
    align-items: center;
    padding: 0 18px;
    background-color: var(--ingatlanbazar-red-450);
    @include container-breakpoint-down(sm) {
      display: none;
    }
  }

  .real-estate-more-icon {
    width: 18px;
    height: 18px;
  }

  .real-estate-tabs {
    display: flex;
    margin-bottom: 10px;
  }

  .real-estate-tabs-item {
    margin-right: 24px;

    &:last-child {
      margin-right: 0;
    }
  }

  .real-estate-tabs-button {
    font-family: var(--kui-font-roboto-flex);
    font-weight: 700;
    font-size: 17px;
    line-height: 21px;
    letter-spacing: 0.7px;
    text-transform: uppercase;
    position: relative;
    color: var(--ingatlanbazar-gray-400);

    &.is-active {
      color: var(--ingatlanbazar-red-400);

      &:before {
        content: '';
        position: absolute;
        width: 100%;
        height: 3px;
        background-color: var(--ingatlanbazar-red-400);
        bottom: -1px;
        left: 0;
      }
    }
  }

  .real-estate-more-wrapper {
    display: none;
    margin-top: 10px;

    @include container-breakpoint-down(sm) {
      display: block;

      .real-estate-more {
        display: flex;
        width: 100%;
        height: 50px;
      }
    }
  }

  .real-estate-item-container {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin: 0 -15px;
    @include container-breakpoint-down(xs) {
      flex-wrap: nowrap;
      overflow: auto;
      padding: 0 15px;
    }
  }

  .real-estate-item {
    width: 190px;
    flex-grow: 1;
    margin: 10px 15px 0 15px;

    @include container-breakpoint-down(lg) {
      width: calc(25% - 30px);
    }

    @include container-breakpoint-down(sm) {
      width: calc(33% - 30px);
    }
    @include container-breakpoint-down(xs) {
      width: auto;
      flex: 1 0 200px;
      margin: 0 15px 0 0;
      &:last-child {
        padding-right: 5px;
      }
    }
  }
}
