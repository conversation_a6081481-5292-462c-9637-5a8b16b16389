import { ChangeDetectionStrategy, Component, input, output, signal } from '@angular/core';
import { BaseComponent } from '../base/base.component';
import { SecretDaysCalendar } from '../../definitions';
import { IconComponent } from '../icon/icon.component';
import { ClickOutsideDirective } from '../../directives';

@Component({
  selector: 'kesma-secret-days-calendar',
  templateUrl: './secret-days-calendar.component.html',
  styleUrl: './secret-days-calendar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [IconComponent, ClickOutsideDirective],
})
export class SecretDaysCalendarComponent extends BaseComponent<SecretDaysCalendar> {
  desktopWidth = input<number>(12);
  isSidebar = input<boolean>(false);
  dayClickEvent = output<string>();
  modalCloseEvent = output<void>();

  isModalOpen = signal<boolean>(false);

  openDayModal(id: string): void {
    this.dayClickEvent.emit(id);
    this.isModalOpen.set(true);
  }

  onModalClose(): void {
    this.isModalOpen.set(false);
    this.modalCloseEvent.emit();
  }
}
