@use 'shared' as *;

.calendar {
  &-wrapper {
    display: flex;
    flex-direction: column;
    background-size: cover;
    background-position: center;
    width: 100%;
    padding: 12px;

    &.small {
      .calendar-sponsor-left {
        display: none;
      }

      .calendar-day-list {
        gap: 25px;
      }
    }
  }

  &-sponsorship {
    display: flex;
    gap: 16px;
    align-items: center;
    justify-content: center;

    &.side {
      height: 100%;
    }

    &-logo {
      &.side {
        height: 100%;
        width: 100%;
      }
    }
  }

  &-body {
    display: flex;
    gap: 12px;
  }

  &-sponsor-left {
    width: 30%;
    display: inline-block;
    position: relative;
    overflow: hidden;

    @include media-breakpoint-down(md) {
      display: none;
    }
  }

  &-days-wrapper {
    display: flex;
    gap: 16px;
    background-size: cover;
    background-position: center;
    flex: 1;
  }

  &-day-list {
    padding: 12px;
    display: flex;
    width: 100%;
    gap: 70px;
    flex-wrap: wrap;
    justify-content: space-around;
    align-items: center;

    @include media-breakpoint-down(md) {
      gap: 25px;
    }
  }

  &-day {
    cursor: pointer;
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-size: cover;
    background-position: center;
    min-width: 70px;
    background-color: var(--kui-white);

    &.with-image {
      background-color: transparent;
    }
  }
}
.secret-day-modal {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 10000;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;

  &-content {
    width: max-content;
    max-height: 80vh;
    max-width: 80vw;
    background-color: var(--kui-white);
    padding: 15px;
    overflow-y: auto;
    margin: 30px;
    position: relative;

    kesma-icon {
      position: absolute;
      right: 10px;
      top: 10px;
      color: var(--kui-black);
      cursor: pointer;
    }

    @include media-breakpoint-down(md) {
      max-width: 100%;
      margin: 15px;
    }
  }
}
