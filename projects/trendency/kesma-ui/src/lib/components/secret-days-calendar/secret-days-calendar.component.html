@if (data) {
  <div class="calendar-wrapper" [class.small]="desktopWidth() < 6" [style.background-image]="'url(' + data?.backgroundImage?.fullSizeUrl + ')'">
    @if (data?.sponsorshipForHeader?.title && data?.isSponsorHeaderActive) {
      <a [href]="data?.sponsorshipForHeader?.url" target="_blank" class="calendar-sponsorship">
        <img [src]="data.sponsorshipForHeader?.logo?.thumbnailUrl" alt="{{ data.sponsorshipForHeader?.title }}" class="calendar-sponsorship-logo" />
      </a>
    }
    <div class="calendar-body">
      @if (data?.isSponsorLeftActive) {
        <div class="calendar-sponsor-left">
          <a [href]="data?.sponsorshipMainOnLeft?.url" target="_blank" class="calendar-sponsorship side">
            <img
              [src]="data?.sponsorshipMainOnLeft?.logo?.thumbnailUrl"
              alt="{{ data?.sponsorshipMainOnLeft?.title }}"
              class="calendar-sponsorship-logo side"
            />
          </a>
        </div>
      }
      <div class="calendar-days-wrapper">
        <div class="calendar-day-list">
          @for (day of data?.days; track day.id) {
            @if (day.name) {
              <div
                (click)="openDayModal(day.id)"
                class="calendar-day"
                [class.with-image]="day?.backgroundImageUrl"
                [style.background-image]="'url(' + day?.backgroundImageUrl + ')'"
              >
                <p class="calendar-day-name" [style.color]="data.colorOfDays">
                  {{ day.name }}
                </p>
              </div>
            }
          }
        </div>
      </div>
    </div>

    @if (data?.sponsorshipForFooter?.title && data?.isSponsorFooterActive) {
      <a [href]="data?.sponsorshipForFooter?.url" target="_blank" class="calendar-sponsorship">
        <img [src]="data?.sponsorshipForFooter?.logo?.thumbnailUrl" alt="{{ data.sponsorshipForFooter?.title }}" class="calendar-sponsorship-logo" />
      </a>
    }
  </div>

  @if (isModalOpen()) {
    <div class="secret-day-modal">
      <div class="secret-day-modal-content" (clickOutside)="onModalClose()">
        <kesma-icon [name]="'close'" [size]="24" (click)="onModalClose()"></kesma-icon>
        <ng-content></ng-content>
      </div>
    </div>
  }
}
