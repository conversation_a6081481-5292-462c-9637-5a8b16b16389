:host {
  .box {
    &-wrapper {
      border: 1px solid #ccc;
      width: 100%;
      font-family: sans-serif;
    }
    &-header {
      padding: 20px;
      background-size: cover;
      background-position: center;
      img {
        max-width: 100%;
        height: auto;
      }
    }
    &-body {
      padding: 20px;
      background-size: cover;
      background-position: center;
      ::ng-deep {
        .article-card-title {
          color: var(--kui-variable-sponsored-did-you-know-title-color) !important;
        }
      }
      h2 {
        margin-top: 0;
        font-size: 1.2rem;
        line-height: 1.8rem;
        font-weight: bold;
      }
      .article-wrapper {
        margin: 10px 0 0 0;
      }
      .description {
        font-size: 1rem;
        margin: 10px 0 0 0;
      }
    }
    &-footer {
      padding: 20px;
      background-size: cover;
      background-position: center;
    }
    .footer-text {
      font-style: italic;
      font-size: 0.9rem;
      font-weight: 600;
    }
  }
  .ad-text {
    text-align: center;
    font-size: 10px;
    color: var(--kui-slate-950);
    padding: 5px 0;
  }
}
