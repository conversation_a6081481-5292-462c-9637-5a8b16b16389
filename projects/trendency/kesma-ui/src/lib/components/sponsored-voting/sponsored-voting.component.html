<ng-container *ngIf="vm.state$ | async as state">
  <div class="voting-header">
    <span class="voting-header-text">Szavazás</span>
    <div class="voting-header-sponsor"></div>
  </div>
  <div class="voting-banner"></div>
  <div class="voting-title-box">
    <h2 class="voting-title-box-title">{{ data?.title }}</h2>
  </div>

  <h3 class="voting-question">{{ data?.question }}</h3>

  <div class="voting-form">
    <ul class="voting-progress-list">
      <li class="voting-progress-list-item" *ngFor="let item of data?.answers">
        <div class="voting-progress-list-label">
          <input
            [id]="item.id"
            [name]="'answers-' + data?.id"
            class="voting-progress-list-radio-button"
            type="radio"
            [value]="item.id"
            [disabled]="state.showResults"
            [checked]="voteId === item.id"
            (click)="setVoteId(item.id)"
          />
          <label class="voting-progress-list-text" [for]="item.id"> {{ item.answer }} </label>
          <div class="voting-progress-list-percentage" *ngIf="state.showResults">{{ item?.votePercentage }}%</div>
        </div>
        <div class="voting-line" *ngIf="state.showResults">
          <span class="voting-line-inner" [style.Width]="item?.votePercentage + '%'"></span>
        </div>
      </li>
    </ul>

    <button class="voting-button" [disabled]="state.showResults" [class.voted]="state.showResults" (click)="onVote()">
      <span class="voting-button-text">{{ state.showResults ? 'Szavaztál' : 'Szavazok' }}</span>
    </button>
  </div>
</ng-container>
