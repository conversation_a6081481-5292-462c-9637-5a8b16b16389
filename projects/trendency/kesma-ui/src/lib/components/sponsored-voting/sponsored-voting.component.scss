@use '../../../scss/shared' as *;

:host {
  display: block;
  border: 1px solid var(--kui-sponsored-vote-border, var(--kui-gray-400));
  border-top: 10px solid var(--kui-sponsored-vote-border-top);
  font-family: var(--kui-font-primary);
  max-width: 616px;
  width: 100%;
  background-color: var(--kui-gray-100);

  .voting {
    &-header {
      display: flex;
      gap: 10px;
      justify-content: space-between;
      background-color: var(--kui-sponsored-vote-header-background, var(--kui-black));
      color: var(--kui-sponsored-vote-header-text, var(--kui-white));
      padding: 10px;
    }
    &-progress-list {
      margin: 0 10%;

      @include media-breakpoint-down(sm) {
        margin: 0;
      }
    }

    &-progress-list-item {
      display: block;
      margin-bottom: 20px;
      margin-left: 10px;
      margin-right: 10px;
      background: var(--kui-white);
      border: 1px solid var(--kui-gray-400);
      border-radius: 4px;
      padding: 10px;
    }

    &-progress-list-label {
      display: flex;
      font-family: var(--kui-font-primary);
      color: var(--kui-black);
      font-weight: 300;
      font-size: 16px;
      line-height: 16px;
    }

    &-progress-list-radio-button {
      display: flex;
      align-self: flex-start;
      margin-right: 10px;
    }

    &-progress-list-text {
      margin-right: 5px;
    }

    &-progress-list-percentage {
      display: contents;
      font-weight: 700;
      margin: 0 5px;
    }

    &-question {
      display: flex;
      font-style: normal;
      font-weight: 700;
      font-size: 21px;
      line-height: 24px;
      color: var(--kui-black);
      justify-content: center;
      text-align: center;
      margin-bottom: 20px;
    }

    &-title-box {
      display: flex;
      justify-content: center;
      width: fit-content;
      margin: 0 auto 20px auto;
      text-align: center;
      padding: 0 15px;
    }

    &-title-box-title {
      font-size: 16px;
      font-weight: 700;
      text-transform: uppercase;
      color: var(--kui-black);
      text-align: center;
    }

    &-line {
      height: 4px;
      background-color: var(--kui-gray-400);
      display: flex;
      align-items: center;
      margin-top: 10px;
    }

    &-line-inner {
      display: block;
      height: 4px;
      background-color: var(--kui-sponsored-vote-line-background, var(--kui-black));
    }

    &-button {
      display: block;
      background-color: var(--kui-sponsored-vote-submit-background, var(--kui-black));
      color: var(--kui-sponsored-vote-submit-text, var(--kui-white));
      margin: 20px auto 10px auto;
      width: 100%;
      max-width: 260px;
      height: 40px;

      &.voted {
        background-color: var(--kui-gray-200);
        cursor: auto;
      }
    }

    &-button-text {
      text-transform: uppercase;
    }

    &-recaptcha {
      display: flex;
      justify-content: center;
      margin: 0 auto;
    }
  }
}
