import { ChangeDetectionStrategy, Component, HostBinding, OnInit } from '@angular/core';
import { SponsoredVoteData } from '../../definitions';
import { AsyncPipe, NgFor, NgIf } from '@angular/common';
import { VotingComponent } from '../voting/voting.component';

@Component({
  selector: 'kesma-sponsored-voting',
  templateUrl: './sponsored-voting.component.html',
  styleUrls: ['./sponsored-voting.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [NgIf, NgFor, AsyncPipe],
})
export class SponsoredVotingComponent extends VotingComponent<SponsoredVoteData> implements OnInit {
  @HostBinding('attr.adocean-id')
  get adoceanTargetId(): string {
    return this.data?.adOceanId || '';
  }
}
