import { AfterViewInit, Component, DestroyRef, ElementRef, inject, OnD<PERSON>roy, signal, ViewChild } from '@angular/core';
import { NgClass, NgStyle } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { fromEvent } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { UtilService } from '@trendency/kesma-core';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'kesma-floating-video',
  templateUrl: './floating-video.component.html',
  styleUrl: './floating-video.component.scss',
  imports: [NgClass, NgStyle, IconComponent],
})
export class FloatingVideoComponent implements AfterViewInit, OnDestroy {
  @ViewChild('videoContainer', { static: true }) videoContainer!: ElementRef;
  @ViewChild('video', { static: true }) video!: ElementRef;

  readonly #utilService = inject(UtilService);
  readonly #destroyRef = inject(DestroyRef);

  videoHeight = signal<number>(0);
  isFloating = signal<boolean>(false);

  isClosed = false;

  observer: IntersectionObserver | undefined;

  ngAfterViewInit(): void {
    if (this.#utilService.isBrowser()) {
      fromEvent(window, 'resize')
        .pipe(
          startWith(null),
          map(() => this.video.nativeElement.offsetHeight),
          takeUntilDestroyed(this.#destroyRef)
        )
        .subscribe((videoHeight: number) => {
          this.isFloating.set(false);
          this.videoHeight.set(videoHeight);
        });

      this.runObserver();
    }
  }

  runObserver(): void {
    this.observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (!this.isClosed) {
          this.isFloating.set(entry.boundingClientRect.bottom <= 0);
        }
      });
    });

    this.observer.observe(this.videoContainer.nativeElement);
  }

  close(): void {
    this.isFloating.set(false);
    this.isClosed = true;
  }

  ngOnDestroy(): void {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}
