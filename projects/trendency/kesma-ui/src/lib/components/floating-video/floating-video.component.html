<div #videoContainer>
  @if (isFloating()) {
    <div class="video-placeholder" [ngStyle]="{ height: videoHeight() + 'px' }"></div>
  }

  <div #video class="video-wrapper" [ngClass]="{ floating: isFloating() }">
    @if (isFloating()) {
      <kesma-icon [name]="'clear'" [size]="10" (click)="close()"></kesma-icon>
    }
    <div class="video-object">
      <ng-content></ng-content>
    </div>
  </div>
</div>
