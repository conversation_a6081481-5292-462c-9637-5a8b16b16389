:host {
  ::ng-deep .video-tn {
    margin-bottom: 0 !important;
  }
}

.video {
  &-placeholder {
    width: 100%;
    height: auto;
  }

  &-wrapper {
    kesma-icon {
      cursor: pointer;
      display: inline-flex;
      padding: 10px 0 10px 10px;
    }

    &.floating {
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 300px;
      height: auto;
      z-index: 10001; // Larger than ads
      text-align: right;

      .video-object {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }
    }
  }
}
