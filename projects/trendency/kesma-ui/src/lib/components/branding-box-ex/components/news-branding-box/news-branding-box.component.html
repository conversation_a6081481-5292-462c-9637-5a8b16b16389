<div class="box-container">
  <div class="header">
    <a class="header-link" href="https://hirado.hu/" target="_blank" aria-label="Híradó">
      <img src="/assets/images/hirado-logo.svg" alt="Híradó" class="header-logo" loading="lazy" />
    </a>
  </div>
  <div *ngIf="articles" class="big">
    <div class="main-article-container">
      <ng-container *ngTemplateOutlet="mainArticle; context: { $implicit: articles[0] }"></ng-container>
    </div>
  </div>
  <div *ngIf="articles" class="small1">
    <ng-container *ngTemplateOutlet="smallArticleList; context: { $implicit: articles | slice: 1 : 3, showLastOnMobile: true }"></ng-container>
  </div>
  <div *ngIf="articles" class="small2">
    <ng-container *ngTemplateOutlet="smallArticleList; context: { $implicit: articles | slice: 3 : 5 }"></ng-container>
  </div>
</div>

<ng-template #mainArticle let-article>
  <div *ngIf="article" class="main-article">
    <div class="image-container">
      <a *ngIf="article.imageUrl" [href]="article.url" target="_blank" [attr.aria-label]="article.title">
        <img [src]="article.imageUrl" alt="" class="image" loading="lazy" />
      </a>
    </div>
    <a [href]="article.url" target="_blank" [attr.aria-label]="article.title">
      <h2 class="title">{{ article.title }}</h2>
      <p *ngIf="article.lead" class="lead">{{ article.lead }}</p>
    </a>
  </div>
  <hr class="article-divider mobile" />
</ng-template>

<ng-template #smallArticle let-article>
  <a [href]="article.url" target="_blank" class="small-article" *ngIf="article" [attr.aria-label]="article.title">
    <img *ngIf="article?.imageUrl" [src]="article.imageUrl" alt="" class="image" loading="lazy" />
    <a [href]="article.url" target="_blank" class="title">{{ article.title }}</a>
  </a>
</ng-template>

<ng-template #smallArticleList let-articles let-showLastOnMobile="showLastOnMobile">
  <ng-container *ngFor="let article of articles; let index = index; let last = last">
    <ng-container *ngTemplateOutlet="smallArticle; context: { $implicit: article }"></ng-container>
    <hr *ngIf="!last || showLastOnMobile" class="article-divider" [class.desktop]="last && showLastOnMobile" />
  </ng-container>
</ng-template>
