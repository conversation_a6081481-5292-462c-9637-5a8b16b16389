<a class="article" *ngIf="article" target="_blank" [href]="article.url">
  <img class="article-image" *ngIf="type !== 'rest-in-group' && article.thumbnail" [src]="article.thumbnail" loading="lazy" alt="<PERSON><PERSON><PERSON> k<PERSON>" />

  <div class="article-title" [ngClass]="{ 'article-title-main-sized': type === 'main' }">
    {{ article.title }}
  </div>

  <div class="article-description" *ngIf="type === 'main' && article.lead">
    {{ article.lead }}
  </div>
</a>
