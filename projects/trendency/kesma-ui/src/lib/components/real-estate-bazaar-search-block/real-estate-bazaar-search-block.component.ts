import { ChangeDetectionStrategy, Component, Input, OnChanges, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import {
  RealEstateBazaarFormData,
  RealEstateBazaarLocation,
  RealEstateBazaarType,
  realEstateBazaarTypes,
  realEstateBudapestLocations,
  realEstateCountyLocations,
  realEstateOtherLocations,
  realEstateCountyLocationsWithBudapest,
} from './real-estate-bazaar-search-block.definitions';
import { NgFor, NgIf } from '@angular/common';

@Component({
  selector: 'kesma-real-estate-bazaar-search-block',
  templateUrl: './real-estate-bazaar-search-block.component.html',
  styleUrls: ['./real-estate-bazaar-search-block.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, FormsModule, ReactiveFormsModule, NgFor],
})
export class RealEstateBazaarSearchBlockComponent implements OnInit, OnChanges {
  @Input() showBudapestLocations: boolean = true;
  @Input() showCountyLocations: boolean = false;
  @Input() showOtherLocations: boolean = false;
  @Input() showCountyLocationsWithBudapest = false;

  @Input() showAdvertiseButton: boolean = true;
  @Input() showNewBuildButton: boolean = true;

  @Input() defaultLocation: number | undefined; // If undefined, first list option will be the default
  @Input() defaultType: string | undefined; // If undefined, first list option will be the default

  @Input() utmSource: string | undefined; // For example: 'metropol.hu', if undefined, no utm referral data will be sent

  formGroup: UntypedFormGroup | undefined;

  locations: Array<RealEstateBazaarLocation> = [];
  types: Array<RealEstateBazaarType> = realEstateBazaarTypes;

  ngOnInit(): void {
    this.initLocations();
    this.setDefaults();
    this.initForm();
  }

  ngOnChanges(): void {
    this.initLocations();
    this.setDefaults();
    this.initForm();
  }

  initLocations(): void {
    this.locations = [];

    if (this.showBudapestLocations) {
      this.locations = this.locations.concat(realEstateBudapestLocations);
    }

    if (this.showCountyLocations) {
      this.locations = this.locations.concat(realEstateCountyLocations);
    }

    if (this.showOtherLocations) {
      this.locations = this.locations.concat(realEstateOtherLocations);
    }

    if (this.showCountyLocationsWithBudapest) {
      this.locations = this.locations.concat(realEstateCountyLocationsWithBudapest);
    }
  }

  setDefaults(): void {
    if (!this.defaultLocation) {
      this.defaultLocation = this.locations.length > 0 ? this.locations[0].id : undefined;
    }
    if (!this.defaultType) {
      this.defaultType = this.types.length > 0 ? this.types[0].id : undefined;
    }
  }

  initForm(): void {
    this.formGroup = new UntypedFormGroup({
      location: new UntypedFormControl(this.defaultLocation, Validators.required),
      type: new UntypedFormControl(this.defaultType, Validators.required),
      min: new UntypedFormControl(undefined, [Validators.min(0), Validators.max(999999), Validators.pattern('^[0-9]*$')]),
      max: new UntypedFormControl(undefined, [Validators.min(0), Validators.max(999999), Validators.pattern('^[0-9]*$')]),
    });
  }

  search(): void {
    const formData: RealEstateBazaarFormData = this.formGroup?.value;
    const url =
      `https://www.ingatlanbazar.hu/ingatlan` +
      `?property_location=${formData?.location ?? ''}` +
      `&property__2=${formData?.type ?? ''}` +
      `&price_min=${formData?.min ?? ''}` +
      `&price_max=${formData?.max ?? ''}` +
      (this.utmSource ? `&utm_source=${this.utmSource}&utm_medium=referral&utm_campaign=ib_ingatlankereso_keres` : ``);
    window.open(url, '_blank');
  }

  advertise(): void {
    const url =
      `https://www.ingatlanbazar.hu/maganhirdetoknek` +
      (this.utmSource ? `?utm_source=${this.utmSource}&utm_medium=referral&utm_campaign=ib_ingatlankereso_hirdet` : ``);
    window.open(url, '_blank');
  }

  showNewBuildOnly(): void {
    const url =
      `https://www.ingatlanbazar.hu/ingatlan` +
      `?property_location=${this.defaultLocation ?? ''}` +
      `&property__2=${this.defaultType ?? ''}` +
      `&property_newbuildonly=on` +
      (this.utmSource ? `&utm_source=${this.utmSource}&utm_medium=referral&utm_campaign=ib_ingatlankereso_ujepitesu` : ``);
    window.open(url, '_blank');
  }
}
