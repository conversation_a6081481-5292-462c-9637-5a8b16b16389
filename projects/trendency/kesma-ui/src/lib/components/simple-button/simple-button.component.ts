import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { ButtonColor, ButtonRoundness, ButtonStyleInfo, IconPositions } from './simple-button.definitions';
import { IconComponent } from '../icon/icon.component';
import { Ng<PERSON><PERSON>, NgIf, NgStyle, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'simple-button',
  templateUrl: './simple-button.component.html',
  styleUrls: ['./simple-button.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgStyle, NgTemplateOutlet, NgIf, IconComponent, NgClass],
})
export class SimpleButtonComponent {
  @Input() disabled = false;
  @Input() isSubmit = false;
  @Input() icon?: string;
  @Input() iconPosition: IconPositions = 'left';
  @Input() color: ButtonColor = 'primary';
  @Input() iconPrefix = 'icon-';

  @Input() styleInfo: ButtonStyleInfo = {};

  @Input() useKesmaIcon = false;
  @Input() kesmaIconSize?: number;

  @Input() ariaLabel?: string;

  /** Fill available space */
  @HostBinding('class.w-100') @Input() wide = false;

  @Input() @HostBinding('class') round: ButtonRoundness = 'normal';
}
