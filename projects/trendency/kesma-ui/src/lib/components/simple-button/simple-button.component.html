<button
  [disabled]="disabled"
  [type]="isSubmit ? 'submit' : 'button'"
  class="btn-{{ color }}"
  [ngStyle]="styleInfo?.button || null"
  [attr.aria-label]="ariaLabel"
>
  @if (icon && iconPosition === 'left') {
    <ng-container [ngTemplateOutlet]="iconTemplate" [ngTemplateOutletContext]="{ position: 'left' }"></ng-container>
  }
  @if (icon && iconPosition === 'center') {
    <ng-container [ngTemplateOutlet]="iconTemplate" [ngTemplateOutletContext]="{ position: 'center' }"></ng-container>
  }
  <span *ngIf="!(icon && iconPosition === 'center')" [ngStyle]="styleInfo?.content || null">
    <ng-content></ng-content>
  </span>
  @if (icon && iconPosition === 'right') {
    <ng-container [ngTemplateOutlet]="iconTemplate" [ngTemplateOutletContext]="{ position: 'right' }"></ng-container>
  }
</button>

<ng-template #iconTemplate let-position="position">
  @if (useKesmaIcon && icon) {
    <kesma-icon
      [name]="icon"
      [size]="kesmaIconSize"
      class="icon"
      [ngClass]="[icon, iconPosition === 'left' ? 'me-1' : '', iconPosition === 'right' ? 'ms-1' : '']"
      [ngStyle]="styleInfo?.icon || null"
    ></kesma-icon>
  } @else {
    <i
      class="icon {{ iconPrefix }}{{ icon }} me-1"
      [class.me-1]="position === 'left'"
      [class.ms-1]="position === 'right'"
      [ngStyle]="styleInfo?.icon || null"
    ></i>
  }
</ng-template>
