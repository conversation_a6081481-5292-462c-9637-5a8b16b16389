<div class="swipe-container" [ngClass]="containerClasses()">
  <div class="item-container" #itemContainer>
    <ng-container *ngFor="let d of data(); trackBy: trackByFn; let i = index">
      <div class="item" [class.active]="i === currentLeftIndex()" #item>
        <ng-container [ngTemplateOutlet]="itemTemplate()" [ngTemplateOutletContext]="{ data: d, index: i }"> </ng-container>
      </div>
    </ng-container>
  </div>
</div>
<div class="bottom-navigation">
  @if (useNavigation() && data()?.length > 1 && !(isAtStart() && isAtEnd())) {
    <button
      (click)="swipePageBack()"
      [disabled]="isAtStart()"
      class="navigation-button navigation-button-left"
      [class.disabled]="isAtStart()"
      aria-label="Előző"
    >
      @if (previousNavigationTemplate()) {
        <ng-container [ngTemplateOutlet]="previousNavigationTemplate()"></ng-container>
      }
    </button>
  }
  @if (usePagination()) {
    @if (paginationDots().length > 1) {
      @for (paginationIndex of paginationDots(); track paginationIndex; let i = $index) {
        <button class="pagination-dot" [class.active]="i === currentPage()" (click)="swipeTo(paginationIndex)" [attr.aria-label]="i + 1 + '. tartalom'">
          @if (bulletTemplate()) {
            <ng-container [ngTemplateOutlet]="bulletTemplate()"></ng-container>
          }
        </button>
      }
    }
  }
  @if (useNavigation() && data()?.length > 1 && !(isAtStart() && isAtEnd())) {
    <button
      (click)="swipePageForward()"
      [disabled]="isAtEnd()"
      class="navigation-button navigation-button-right"
      [class.disabled]="isAtEnd()"
      aria-label="Következő"
    >
      @if (nextNavigationTemplate()) {
        <ng-container [ngTemplateOutlet]="nextNavigationTemplate()"></ng-container>
      }
    </button>
  }
</div>
