import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  computed,
  DestroyRef,
  effect,
  ElementRef,
  HostBinding,
  inject,
  input,
  OnD<PERSON>roy,
  Renderer2,
  Signal,
  signal,
  TemplateRef,
  viewChild,
  viewChildren,
} from '@angular/core';
import { NgClass, NgForOf, NgTemplateOutlet } from '@angular/common';
import { generateSwipeKey } from './swipe.utils';
import { UtilService } from '@trendency/kesma-core';
import { animationFrameScheduler, fromEvent, throttleTime } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SwipeBreakpoints } from './swipe.definitions';
import { KesmaSwipeService } from '../../services';
import { isObject } from 'lodash-es';

/**
 * Custom Swipe component to achieve all the required functionalities that we need without
 * Swiper or any 3rd party lib. This solution uses the browser's built in scrolling and scroll snapping
 * behaviour, to be as lightweight as possible.
 * You can find more information on the dedicated Confluence page:
 */
@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: '[kesma-swipe]', // Explicitly using a selector to avoid having a dedicated wrapper element as it already needs many layers of DOM element.
  templateUrl: './swipe.component.html',
  styleUrls: ['./swipe.component.scss'],
  imports: [NgTemplateOutlet, NgForOf, NgClass],
})
export class KesmaSwipeComponent<T extends object> implements AfterViewInit, OnDestroy {
  @HostBinding('class') hostClass = 'kesma-swipe';
  readonly startIndex = input(0);
  /**
   * Allows to display any custom content for each slide item.
   */
  readonly itemTemplate = input.required<TemplateRef<{ data: T; index: number }>>();
  /**
   * Controls how each pagination bullet will look like. You can provide your own template to override the default one.
   */
  readonly bulletTemplate = input<TemplateRef<never>>();
  /**
   * Content for the previous navigation button
   */
  readonly previousNavigationTemplate = input<TemplateRef<never>>();
  /**
   * Content for the next navigation button
   */
  readonly nextNavigationTemplate = input<TemplateRef<never>>();
  /**
   * List of slides that will be used. You can provide any type of arrays, but keep in mind that you might need to adjust
   * the dataTrackByProperty. By default, it will look for id property in objects.
   */
  readonly data = input.required<T[]>();
  /**
   * Controls how each item in the provided data array will be tracked. By default it tracks by the id property.
   * If your items don't have an id property, provide your own property key.
   */
  readonly dataTrackByProperty = input<string>('id');
  /**
   * Stores the current item alignment mode.
   */
  protected readonly _currentItemAlign = signal<string>('start');
  /**
   * Returns which alignment mode is currently used. The alignment mode can change between different breakpoints.
   */
  readonly currentItemAlign = computed(() => this._currentItemAlign());
  /**
   * You can set custom look and behaviours for different screen breakpoints. The breakpoints are controlled by
   * container query, so it depends on the size of the swipe component size, not on the screen size.
   * It is important, as Swiper was always used regular media queries, so migrating from Swiper needs some checking about
   * their own breakpoints.
   * Each breakpoint has a key which is the minimum pixel width that needs to be justified in order for that breakpoint
   * to be resolved. You can provide a breakpoint with a default key, which is basically the default fallback for the smallest size.
   * If you don't want to specify breakpoints, you can simply pass just the default key with your desired settings.
   */
  readonly breakpoints = input.required<SwipeBreakpoints>();
  /**
   * Turns the pagination on or off. If there is only one page, the pagination buttons will not show.
   */
  readonly usePagination = input(false);
  /**
   * Turns the navigation on or iff. When turned on the prev/next navigation buttons will be visible.
   */
  readonly useNavigation = input(false);
  protected readonly _isAtStart = signal(false);
  /**
   * Shows when we are the beginning of the slides.
   */
  readonly isAtStart = computed(() => this._isAtStart());
  protected readonly _isAtEnd = signal(false);
  /**
   * Shows when we are the end of the slides.
   */
  readonly isAtEnd = computed(() => this._isAtEnd());

  readonly containerClasses = computed(() => {
    const classes = [this.styleKey()];
    if (this.isAtStart()) {
      classes.push('at-start');
    }
    if (this.isAtEnd()) {
      classes.push('at-end');
    }
    return classes.join(' ');
  });

  /**
   * This is the unique key that represents the breakpoint config that we use. The generated styles are generated using
   * the breakpoints. But two swipe components can have the same styles based on their breakpoint config, so in order
   * to save some CSS, there are no duplicate styles added to the DOM. Each breakpoint config will receive a style key
   * that is unique for that given config, but will be always the same for the given breakpoint configs.
   */
  readonly styleKey = computed(() => generateSwipeKey(this.breakpoints()));

  /**
   * Returns the index of the item that represents the current page that we are on it. Pages are defined using the
   * indexes of each item. These indexes are stored in the paginationDots signal, which could change based on the current
   * breakpoint config.
   */
  readonly currentPage = computed(() => {
    return this.getPaginationIndex(this.currentLeftIndex(), this.paginationDots());
  });
  /**
   * Stores the active index from left snapping.
   * @see {@link this.currentIndex} for further information about index calculation
   */
  protected readonly currentLeftIndex = signal(0);
  /**
   * Returns the current slide index that is active based on the current breakpoint config.
   * If there are multiple slides shown on one page, the index could any of the shown slides based on the given snapping
   * config (itemAlign) that is in the current breakpoint.
   */
  readonly currentIndex = computed(() => this.currentLeftIndex());
  /**
   * Number array that contains the item indexes that indicate which page starts with which item index.
   */
  readonly paginationDots = computed(() => {
    const itemsPerPage = this.pageSize() || this._shownItems();
    if (itemsPerPage === 0 || typeof itemsPerPage === 'string') {
      return [];
    }
    const itemCount = this.data().length;
    const totalPages = Math.ceil(itemCount / itemsPerPage);
    const paginationDots = [];

    for (let i = 0; i < totalPages; i++) {
      paginationDots.push(i * itemsPerPage);
    }
    return paginationDots;
  });
  protected readonly cdr = inject(ChangeDetectorRef);
  protected readonly service = inject(KesmaSwipeService);
  protected readonly renderer2 = inject(Renderer2);
  protected readonly utils = inject(UtilService);
  protected readonly destroyRef = inject(DestroyRef);

  protected readonly itemContainerElement: Signal<ElementRef<HTMLDivElement> | undefined> = viewChild('itemContainer', { read: ElementRef });
  protected readonly itemElements = viewChildren('item', { read: ElementRef });
  protected readonly _shownItems = signal(0);
  protected readonly _pageSize = signal<number | string | null>(null);
  readonly pageSize = computed(() => this._pageSize());
  readonly #isViewInitialized = signal(false);

  constructor() {
    effect(() => {
      const itemContainer = this.itemContainerElement()?.nativeElement;
      if (!itemContainer) {
        return;
      }
      if (!this.utils.isBrowser()) {
        return;
      }
      const resizeObserver = new ResizeObserver(() => {
        const value = getComputedStyle(itemContainer).getPropertyValue('--swipe-item-count').trim();
        const pageSize = getComputedStyle(itemContainer).getPropertyValue('--swipe-page-size').trim();
        const itemAlign = getComputedStyle(itemContainer).getPropertyValue('--swipe-item-align').trim();
        const isPageSizeNumber = pageSize?.length && !isNaN(+pageSize);
        this._pageSize.set(isPageSizeNumber ? +pageSize : pageSize || null);
        const intValue = parseInt(value);
        if (itemAlign !== this.currentItemAlign()) {
          this._currentItemAlign.set(itemAlign);
        }
        if (intValue !== this._shownItems()) {
          this._shownItems.set(intValue);
          this.cdr.detectChanges();
        }
      });
      resizeObserver.observe(itemContainer);
      fromEvent(itemContainer, 'scroll')
        .pipe(throttleTime(50, animationFrameScheduler, { leading: true, trailing: true }))
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe(() => {
          this.currentLeftIndex.set(this.getLeftIndex());
          this.updateIsAtStartEnd();
          this.cdr.detectChanges();
        });
    });
    effect(() => {
      const breakpoints = this.breakpoints();
      this.service.useBreakpoints(this.renderer2, this.styleKey(), breakpoints);
    });
    effect(() => {
      if (!this.#isViewInitialized()) {
        return;
      }
      if (!this.data()?.length) {
        return;
      }

      this.updateIsAtStartEnd();
    });
  }

  ngAfterViewInit(): void {
    this.#isViewInitialized.set(true);
    if (this.startIndex() > 0) {
      this.swipeTo(this.startIndex(), true);
    }
  }

  protected getLeftIndex(): number {
    const itemContainer = this.itemContainerElement()?.nativeElement;
    if (!itemContainer) {
      return 0;
    }
    const items: Element[] = Array.from(itemContainer.children);

    const leftPosition = items
      .map((item) => {
        const rect = item.getBoundingClientRect();
        const containerRect = itemContainer.getBoundingClientRect();
        const leftInContainer = rect.left - containerRect.left;
        // due to rounding, -1 is required
        return Math.abs(leftInContainer - 1);
      })
      .reduce((minIdx, num, idx, array) => (num < array[minIdx] ? idx : minIdx), 0);

    return leftPosition || 0;
  }

  protected getPaginationIndex(index: number, paginationIndexes: number[]): number {
    let currentPaginationIndex = 0;
    if (this.isAtStart()) {
      return 0;
    }

    for (let i = 0; i < paginationIndexes.length; i++) {
      if (paginationIndexes[i] > index) {
        break;
      }
      currentPaginationIndex = i;
    }
    // When there are even number of items displayed, but odd number of items in the list, we need to explicitly check if we are at the end of the list.
    if (currentPaginationIndex + 2 === paginationIndexes.length && index > paginationIndexes[currentPaginationIndex] && this.isAtEnd()) {
      currentPaginationIndex++;
    }

    return currentPaginationIndex;
  }

  protected trackByFn: (index: number, item: T) => string = (_index: number, item: T) => {
    const prop = this.dataTrackByProperty();
    if (!prop) {
      return item;
    }
    if (isObject(item) && prop in item) {
      return (item as any)[prop];
    }
    return item;
  };

  swipeToLeftPx(left: number): void {
    const container = this.itemContainerElement()?.nativeElement;
    if (!container) {
      return;
    }
    container.scrollTo({ left: left, behavior: 'smooth' });
  }

  swipeTo(index: number, isInstant = false): void {
    const container = this.itemContainerElement()?.nativeElement;
    const child = this.itemElements()?.at(index)?.nativeElement;
    if (!child || !container) {
      return;
    }
    const left = child.offsetLeft;
    container.scrollTo({ left: left, behavior: isInstant ? 'instant' : 'smooth' });
  }

  swipeToPage(pageIndex: number): void {
    const pages = this.paginationDots();
    if (pageIndex >= pages.length || pageIndex < 0) {
      return;
    }
    const itemIndex = pages[pageIndex];
    this.swipeTo(itemIndex);
  }

  swipePageBack(): void {
    const container = this.itemContainerElement()?.nativeElement;
    if (!container) {
      return;
    }
    if (this.pageSize() === 'auto') {
      const width = container.offsetWidth;
      const scrollLeft = container.scrollLeft;
      this.swipeToLeftPx(scrollLeft - width);
      return;
    }
    const currentPage = this.currentPage();
    const currentLeftIndex = this.currentLeftIndex();
    const currentPageItemIndex = this.paginationDots()[currentPage];
    if (currentLeftIndex > currentPageItemIndex) {
      this.swipeToPage(currentPage);
      return;
    }

    this.swipeToPage(currentPage - 1);
  }

  swipePageForward(): void {
    const container = this.itemContainerElement()?.nativeElement;
    if (!container) {
      return;
    }
    if (this.pageSize() === 'auto') {
      const width = container.offsetWidth;
      const scrollLeft = container.scrollLeft;
      this.swipeToLeftPx(scrollLeft + width);
      return;
    }
    const currentPage = this.currentPage();
    this.swipeToPage(currentPage + 1);
  }

  protected updateIsAtStartEnd(): void {
    const isStart = this.calculateIsAtStart();
    const isEnd = this.calculateIsAtEnd();
    const currentIsStart = this.isAtStart();
    const currentIsEnd = this.isAtEnd();
    if (isStart === currentIsStart && isEnd === currentIsEnd) {
      return;
    }
    this._isAtStart.set(isStart);
    this._isAtEnd.set(isEnd);
  }

  protected calculateIsAtStart(): boolean {
    const container = this.itemContainerElement()?.nativeElement;
    return container ? container.scrollLeft === 0 : true;
  }

  protected calculateIsAtEnd(): boolean {
    const container = this.itemContainerElement()?.nativeElement;
    if (!container) {
      return true;
    }
    // we need to use ceil as sometimes the scroll position is not the exact number as the scrollWidth. The difference is sometimes .1 pixel or something.
    // we need +2 because we have a bug (KESMA-26245)
    return Math.ceil(container.scrollLeft + container.clientWidth) + 2 >= container.scrollWidth;
  }

  ngOnDestroy(): void {
    this.service.unUseBreakpoints(this.renderer2, this.styleKey());
  }
}
