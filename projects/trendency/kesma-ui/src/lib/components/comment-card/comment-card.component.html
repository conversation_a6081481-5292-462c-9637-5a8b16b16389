<div class="comment-card-header">
  <div class="comment-card-name">
    <ng-container *ngIf="userNameTemplate; else defaultUserNameTemplate" [ngTemplateOutlet]="userNameTemplate"></ng-container>
    <ng-template #defaultUserNameTemplate>
      <ng-container *ngIf="!isLoggedIn; else loggedInName">{{ data?.author?.username }}</ng-container>
      <ng-template #loggedInName>
        <a [routerLink]="['/', 'profil', 'hozzaszolasok', data?.author?.uid]">{{ data?.author?.username }}</a>
      </ng-template>

      <span *ngFor="let user of responseTo">
        <i [ngClass]="['icon', 'icon-' + answerToDividerIcon]"></i>
        <span class="comment-card-parent">
          <ng-container *ngIf="!isLoggedIn; else loggedInChildName">{{ user?.username }}</ng-container>
          <ng-template #loggedInChildName>
            <a [routerLink]="['/', 'profil', 'hozzaszolasok', user?.uid]">{{ user?.username }}</a>
          </ng-template>
        </span>
      </span>
    </ng-template>
  </div>
  <div *ngIf="canOpenOptions" class="right">
    <ng-container *ngIf="hasPopupOptions; else nonPopupOptions">
      <a
        (click)="onSwitchOptions()"
        (visibleChange)="handleOptionsVisibleChange($event)"
        [class.active]="optionsShown"
        [popover]="popover"
        [popupCloseOnScroll]="popupOptions.closeOnScroll || false"
        [popupInPlace]="popupOptions.inPlace || false"
        [popupOffsetX]="popupOptions.offset?.x || '-50%'"
        [popupOffsetY]="popupOptions.offset?.y || '10px'"
        [popupPosition]="popupOptions.position || 'below'"
        [popupTrigger]="popupOptions.trigger || 'click'"
        class="comment-card-options clickable"
      >
        <div class="options-icon">
          <ng-container *ngIf="optionsIcon; else tripleBullet">
            <i [ngClass]="['icon', optionsIcon]"></i>
          </ng-container>
        </div>
        <!-- css content -->
      </a>
    </ng-container>

    <ng-template #nonPopupOptions>
      <a (click)="onSwitchOptions()" [class.active]="optionsShown" class="comment-card-options clickable">
        <div class="options-icon">
          <ng-container *ngIf="optionsIcon; else tripleBullet">
            <i [ngClass]="['icon', optionsIcon]"></i>
          </ng-container>
        </div>
        <!-- css content -->
      </a>
    </ng-template>

    <ng-template #tripleBullet> &bullet;&bullet;&bullet; </ng-template>

    <!-- Options are VERY portal specific and can vary based on authorization, so I created an outlet for it. This menu should be populated on the portal.  -->
    <ng-template #popover>
      <div class="comment-card-options-popout">
        <ng-container [ngTemplateOutlet]="optionsTemplate"></ng-container>
      </div>
    </ng-template>
  </div>
</div>
<div class="comment-card-date" *ngIf="!userNameTemplate">
  <ng-container *ngIf="!altDateFormatting; else alternativeDateFormat">
    {{ data?.createdAt | dfnsFormat: 'yyyy. LLLL dd. HH:mm' | lowercase }}
  </ng-container>
  <ng-container *ngIf="data?.isUpdated"> • <span class="italic">Szerkesztve</span> </ng-container>
</div>
<ng-container *ngIf="hasAdvancedContent; else contentTemplate">
  <ng-content select="[content]"></ng-content>
</ng-container>
<ng-template #contentTemplate>
  <div class="comment-card-text">{{ data?.text }}</div>
</ng-template>

<ng-content select="[afterContent]"></ng-content>
<div class="comment-card-feedback">
  <ng-container *ngIf="!answerButtonOnRight" [ngTemplateOutlet]="answerButton"></ng-container>
  <div (click)="onReaction(true)" *ngIf="canReact" [class.clickable]="!isReadonly" class="comment-card-feedback-item">
    <i *ngIf="upVoteIcon" [ngClass]="['icon', 'icon-' + upVoteIcon]"></i>
    <div class="comment-card-feedback-text">{{ data?.likeCount }}</div>
  </div>
  <div (click)="onReaction(false)" *ngIf="canReact" [class.clickable]="!isReadonly" class="comment-card-feedback-item">
    <i *ngIf="downVoteIcon" [ngClass]="['icon', 'icon-' + downVoteIcon]"></i>
    <div class="comment-card-feedback-text">{{ data?.dislikeCount }}</div>
  </div>
  <ng-container *ngIf="answerButtonOnRight" [ngTemplateOutlet]="answerButton"></ng-container>
  <ng-container *ngFor="let badge of badges">
    <div (click)="onBadgeClicked(badge)" [class.clickable]="!isReadonly" class="comment-card-feedback-item">
      <i *ngIf="badge.icon" [ngClass]="['icon', 'icon-' + badge.icon]"></i>
      <div class="comment-card-feedback-text">{{ badge.text }}</div>
    </div>
  </ng-container>
</div>

<ng-template #answerButton>
  <div (click)="onSwitchAnswerInput()" *ngIf="canAnswer" class="comment-card-feedback-item clickable">
    <ng-container *ngIf="responseFormShown; else respondToThis">
      <i *ngIf="closeFormIcon" [ngClass]="['comment-card-feedback-icon', 'icon', 'icon-' + closeFormIcon]"></i>
      <div class="comment-card-feedback-text answer cancel">{{ closeFormText }}</div>
    </ng-container>
    <ng-template #respondToThis>
      <i *ngIf="openFormIcon" [ngClass]="['comment-card-feedback-icon', 'icon', 'icon-' + openFormIcon]"></i>
      <div class="comment-card-feedback-text answer open">{{ openFormText }}</div>
    </ng-template>
  </div>
</ng-template>

<ng-template #alternativeDateFormat>
  {{ data?.createdAt | dfnsFormat: 'yyyy. MM. dd.' }} <span class="light">|</span> {{ data?.createdAt | dfnsFormat: 'HH:mm' }}
</ng-template>
