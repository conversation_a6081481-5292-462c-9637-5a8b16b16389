@use '../../../scss/shared' as *;

:host {
  --arrow-down-image: url('/assets/images/icons/arrow-down-white.svg');
  background-color: var(--kui-black);
  color: var(--kui-white);
  font-family: var(--kui-font-primary);
  padding: 50px 10px 60px 50px;
  display: flex;
  justify-content: space-between;
  gap: 20px;
  @include media-breakpoint-down(xs) {
    flex-direction: column;
    padding-left: 20px;
  }

  .mwf-head {
    flex: 1 1 23%;
    @include media-breakpoint-down(xs) {
      flex: 1 1 100%;
      margin-bottom: 20px;
    }

    &-logo {
      display: block;
      margin-bottom: 30px;
      transition: all 400ms cubic-bezier(0.47, 1.64, 0.41, 0.8);

      &:hover {
        transform: scale(1.2) translateY(-5px) translateX(12px);
      }

      &,
      img {
        width: 154px;
        height: 34px;
      }

      img {
        object-fit: contain;
      }
    }

    &-disclaimer {
      font-size: 12px;
      line-height: 15px;
      opacity: 0.5;
    }
  }

  .mwf-portfolio {
    flex: 1 1 auto;

    &-link {
      display: block;
      color: var(--kui-white);
      font-size: 12px;
      letter-spacing: 0;
      line-height: 15px;
      padding-bottom: 12px;
    }

    &-categories {
      display: flex;
      flex-wrap: wrap;
      row-gap: 10px;
      margin-bottom: 30px;

      @include media-breakpoint-down(xs) {
        flex-direction: column;
        flex-wrap: nowrap;
      }

      &-label {
        font-size: 16px;
        line-height: 18px;
        color: var(--kui-white);
        font-family: inherit;
        margin-right: 40px;
        opacity: 0.5;
        cursor: pointer;
        transition: all 400ms cubic-bezier(0.47, 1.64, 0.41, 0.8);
        @include media-breakpoint-down(sm) {
          margin-right: 20px;
        }

        &.active,
        &:hover {
          opacity: 1;
        }

        &:after {
          @include media-breakpoint-down(xs) {
            background: var(--arrow-down-image) no-repeat center center;
            content: ' ';
            display: inline-block;
            width: 18px;
            height: 11px;
            margin-left: 10px;
          }
        }
      }
    }

    &-links {
      column-count: 4;
      @include media-breakpoint-down(md) {
        column-count: 2;
      }
      @include media-breakpoint-down(xs) {
        display: none;
      }
    }

    &-mobile-only-links {
      display: none;
      @include media-breakpoint-down(xs) {
        display: flex;
        flex-direction: column;
      }
    }
  }
}
