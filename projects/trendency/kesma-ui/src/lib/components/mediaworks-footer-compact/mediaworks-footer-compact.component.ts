import { ChangeDetectionStrategy, Component, computed, effect, input, signal } from '@angular/core';
import { PortfolioCategory, PortfolioItem } from '../../definitions';
import { NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'kesma-mediaworks-footer-compact',
  templateUrl: './mediaworks-footer-compact.component.html',
  styleUrls: ['./mediaworks-footer-compact.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgTemplateOutlet],
})
export class MediaworksFooterCompactComponent {
  readonly mwLogo = input<string>('/assets/images/mw/mw-logo-2024-red-white.svg');
  readonly data = input.required<PortfolioItem[]>();

  readonly selectedCategory = signal<PortfolioCategory | undefined>(undefined);

  readonly portfolio = computed<PortfolioCategory[]>(() => {
    const items: PortfolioCategory[] = [];
    (this.data() ?? []).forEach((item) => {
      switch (item.level) {
        case '1':
          items.push({
            ...item,
            children: [],
          });
          break;
        case '2':
          items?.[items.length - 1]?.children?.push(item);
          break;
      }
    });
    return items;
  });

  constructor() {
    effect(() => {
      const items = this.portfolio();
      if (items?.length) {
        this.selectedCategory.set(items[0]);
      }
    });
  }
}
