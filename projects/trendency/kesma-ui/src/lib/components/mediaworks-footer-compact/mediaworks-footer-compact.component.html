<div class="mwf-head">
  <a class="mwf-head-logo" href="https://mediaworks.hu/" target="_blank">
    <img alt="Mediaworks Content First" loading="lazy" [src]="mwLogo()" />
  </a>
  <p class="mwf-head-disclaimer">
    Portfóliónk minőségi tartalmat jelent minden olvasó számára. Egyedülálló elérést, orsz<PERSON><PERSON> lefedet<PERSON>éget és változatos megjelenési lehetőséget biztosít.
    Folyamatosan keressük az új irányokat és fejlődési lehetőségeket. Ez jövőnk záloga.
  </p>
</div>
<div class="mwf-portfolio">
  <div class="mwf-portfolio-categories">
    @for (category of portfolio(); track category.id) {
      @if (category?.name) {
        <button (click)="selectedCategory.set(category)" [class.active]="category.id === selectedCategory()?.id" class="mwf-portfolio-categories-label">
          {{ category.name }}
        </button>
      }
      @if (category.id === selectedCategory()?.id) {
        <div class="mwf-portfolio-mobile-only-links">
          <ng-container *ngTemplateOutlet="categoryLinks"></ng-container>
        </div>
      }
    }
  </div>
  <div class="mwf-portfolio-links">
    <ng-container *ngTemplateOutlet="categoryLinks"></ng-container>
  </div>
</div>

<ng-template #categoryLinks>
  @for (link of selectedCategory()?.children; track link.id) {
    <a [href]="link.url" class="mwf-portfolio-link">
      {{ link.name }}
    </a>
  }
</ng-template>
