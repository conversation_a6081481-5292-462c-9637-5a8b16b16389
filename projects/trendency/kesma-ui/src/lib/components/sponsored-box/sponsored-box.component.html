@if (data?.url) {
  <a [href]="data?.url" target="_blank" class="inner" [style.background-color]="backgroundColor" [style.color]="textColor" [attr.adocean-id]="data?.adoceanId">
    <ng-container *ngTemplateOutlet="innerContent"></ng-container>
  </a>
} @else {
  <div class="inner" [style.background-color]="backgroundColor" [style.color]="textColor" [attr.adocean-id]="data?.adoceanId">
    <ng-container *ngTemplateOutlet="innerContent"></ng-container>
  </div>
}

@if (data?.isAdLabelVisible) {
  <div class="ad-label">Hirde<PERSON>s</div>
}

<ng-template #logoTemplate>
  @if (data?.logo) {
    <img
      [src]="data?.logo?.selectedVariant?.publicUrl"
      [alt]="data?.logo?.altText"
      [title]="data?.logo?.title ?? ''"
      [style.min-width]="data?.logoWidth ? data.logoWidth + 'px' : 'auto'"
      [style.min-height]="data?.logoHeight ? data.logoHeight + 'px' : 'auto'"
      [style.max-width]="data?.logoWidth ? data.logoWidth + 'px' : 'auto'"
      [style.max-height]="data?.logoHeight ? data.logoHeight + 'px' : 'auto'"
      [style.width]="data?.logoWidth ? data.logoWidth + 'px' : 'auto'"
      [style.height]="data?.logoHeight ? data.logoHeight + 'px' : 'auto'"
      loading="lazy"
    />
  }
</ng-template>
<ng-template #titleTemplate>
  @if (data?.title) {
    <div class="title" [style.text-align]="data?.titleAlignment || 'left'">
      {{ data?.title }}
    </div>
  }
</ng-template>

<ng-template #innerContent>
  <div class="left">
    @if (data?.logoAlignment === 'left') {
      <ng-container *ngTemplateOutlet="logoTemplate"></ng-container>
    }
    @if (data?.titleAlignment === 'left') {
      <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
    }
  </div>
  <div class="center">
    @if (data?.logoAlignment === 'center') {
      <ng-container *ngTemplateOutlet="logoTemplate"></ng-container>
    }
    @if (data?.titleAlignment === 'center') {
      <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
    }
  </div>
  <div class="right">
    @if (data?.logoAlignment === 'right') {
      <ng-container *ngTemplateOutlet="logoTemplate"></ng-container>
    }
    @if (data?.titleAlignment === 'right') {
      <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
    }
  </div>
</ng-template>
