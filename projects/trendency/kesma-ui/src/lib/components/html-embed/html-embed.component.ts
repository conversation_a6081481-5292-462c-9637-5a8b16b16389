import { BreakpointObserver } from '@angular/cdk/layout';
import { ChangeDetectionStrategy, Component, inject, input } from '@angular/core';
import { RunScriptsDirective, UtilService } from '@trendency/kesma-core';
import { map } from 'rxjs';
import { HtmlEmbed } from './html-embed.definitions';
import { BypassPipe } from '../../pipes';
import { toSignal } from '@angular/core/rxjs-interop';

@Component({
  selector: 'kesma-html-embed',
  templateUrl: './html-embed.component.html',
  styleUrls: ['./html-embed.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [BypassPipe, RunScriptsDirective],
})
export class HtmlEmbedComponent {
  private readonly utilService = inject(UtilService);
  private readonly breakpointObserver = inject(BreakpointObserver);

  readonly data = input.required({
    transform: (original: HtmlEmbed) => ({
      desktop: this.findYoutube(this.findVidea(original.desktop)),
      mobile: this.findYoutube(this.findVidea(original.mobile)),
    }),
  });

  readonly isBrowser = this.utilService.isBrowser();
  readonly deviceType = toSignal(this.breakpointObserver.observe(['(min-width: 768px)']).pipe(map((result) => (!result.matches ? 'mobile' : 'desktop'))), {
    initialValue: 'desktop',
  });

  private findVidea(html: string): string {
    return html?.replace(
      /<iframe width=".{0,6}" height=".{0,6}" src="\/\/videa.hu/g,
      '<iframe style="aspect-ratio: 16/9; width: 100%; height: auto;" src="//videa.hu'
    );
  }

  private findYoutube(html: string): string {
    return html?.replace(
      /<iframe width=".{0,6}" height=".{0,6}" src="https:\/\/www.youtube.com/g,
      '<iframe style="aspect-ratio: 16/9; width: 100%; height: auto;" src="https://www.youtube.com'
    );
  }
}
