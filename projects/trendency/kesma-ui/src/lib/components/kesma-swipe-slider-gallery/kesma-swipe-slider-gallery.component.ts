import { AfterViewInit, ChangeDetectionStrategy, Component, EventEmitter, inject, Input, OnInit, Output, viewChild } from '@angular/core';
import { GalleryData } from '../../definitions';
import { StorageService } from '@trendency/kesma-core';
import { toBool } from '../../utils';
import { SimplifiedGalleryLayerComponent } from '../simplified-gallery-layer/simplified-gallery-layer.component';
import { AdultOverlayComponent } from '../adult-overlay/adult-overlay.component';
import { AsyncPipe, NgFor, NgIf, NgTemplateOutlet } from '@angular/common';
import { ClickStopPropagationDirective } from '../../directives';
import { BaseComponent } from '../base/base.component';
import { KesmaSwipeComponent } from '../swipe';

/**
 * Slider gallery alternative using our own Swipe Component.
 * This class is a copy of the already existing SlideGalleryComponent.
 * While we are removing Swiper, this two component needs to coexist and afterwards we can remove the one that uses Swiper.
 */
@Component({
  selector: 'kesma-slider-gallery',
  template: '',
  styleUrls: ['./kesma-swipe-slider-gallery.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgFor, AdultOverlayComponent, NgTemplateOutlet, NgIf, SimplifiedGalleryLayerComponent, AsyncPipe, ClickStopPropagationDirective],
})
export abstract class KesmaSwipeSliderGalleryComponent extends BaseComponent<GalleryData> implements OnInit, AfterViewInit {
  swipeComponent = viewChild(KesmaSwipeComponent);
  startImageIndex = 0;
  @Input() isInsideAdultArticleBody = false;
  @Input() canOpenLayer = true;
  @Output() imageClicked: EventEmitter<number> = new EventEmitter<number>();
  @Output() onCloseGallery: EventEmitter<void | number> = new EventEmitter<void | number>();

  @Output() onSelectedIndexChanged: EventEmitter<number> = new EventEmitter<number>();
  @Output() layerSlideChanged: EventEmitter<number> = new EventEmitter<number>();
  @Output() slideChanged = new EventEmitter<{ index: number }>();
  isLayerVisible = false;
  isAcceptedAdultContent = false;
  readonly toBool = toBool;
  private readonly storageService = inject(StorageService);

  constructor() {
    super();
    this.isAcceptedAdultContent = this.storageService.getSessionStorageData('isAdultChoice') ?? false;
  }

  @Input() set selectedImageIndex(index: number) {
    this.startImageIndex = index;
    this.swipeComponent()?.swipeTo(index);
  }

  // Show gallery layer by default
  @Input() set displayLayer(value: boolean) {
    this.isLayerVisible = value;
  }

  acceptAdultContent(): void {
    this.isAcceptedAdultContent = true;
    this.storageService.setSessionStorageData('isAdultChoice', this.isAcceptedAdultContent);
  }

  onCloseSliderLayer(currentIndex: number): void {
    this.onCloseGallery.emit();

    if (!this.displayLayer) {
      this.isLayerVisible = false;
      if (this.swipeComponent()?.currentIndex() !== currentIndex) {
        this.swipeComponent()?.swipeTo(currentIndex);
      }
    }
  }

  onOpenSliderLayer(): void {
    if (this.canOpenLayer) {
      this.isLayerVisible = true;
    } else {
      this.imageClicked.emit(this.swipeComponent()?.currentIndex());
    }
  }

  ngAfterViewInit(): void {
    if (this.swipeComponent()?.currentIndex() !== this.startImageIndex) {
      this.swipeComponent()?.swipeTo(this.startImageIndex);
    }
  }
}
