<div class="sponsored-quiz-container" [attr.adocean-id]="data?.adOceanId">
  <div class="sponsored-quiz-title-container">
    <div class="sponsored-quiz-title">
      {{ data?.title }}
    </div>
    <div class="sponsored-quiz-adocean-logo"></div>
  </div>

  <div class="sponsored-quiz-question-wrapper">
    <div class="sponsored-quiz-question-image-container" *ngIf="isNextButtonVisible && (currentQuestion?.image || currentQuestion?.thumbnailUrl)">
      <img
        class="sponsored-quiz-question-image"
        [src]="currentQuestion?.image || currentQuestion?.thumbnailUrl || placeholderImage"
        alt="Quiz question"
        loading="lazy"
      />
    </div>
    <div class="sponsored-quiz-question-adocean-image" *ngIf="!currentQuestion?.image"></div>

    <div class="sponsored-quiz-stepper">
      {{ isNextButtonVisible ? currentQuestionIndex + 1 + ' / ' + data?.questions?.length + ' kérdés' : 'Eredmény' }}
    </div>

    <ng-container *ngIf="!isNextButtonVisible">
      <ng-container *ngTemplateOutlet="ratingImage"></ng-container>
    </ng-container>
  </div>

  <div class="sponsored-quiz-wrapper">
    <div class="sponsored-quiz-question" [class.short]="currentQuestion?.image">
      <h5 class="sponsored-quiz-question-text">
        <ng-container *ngIf="!isNextButtonVisible">
          <ng-container *ngTemplateOutlet="ratingText"></ng-container>
        </ng-container>
        <ng-template [ngIf]="isNextButtonVisible">
          {{ currentQuestion?.title }}
        </ng-template>
      </h5>
    </div>

    <div class="answer-list" *ngIf="isNextButtonVisible">
      <div
        class="answer-list-item"
        *ngFor="let answer of currentQuestion?.answers; let answerIndex = index"
        [class.wrong]="!answer.isCorrect && givenAnswers[currentQuestionIndex] === answerIndex"
        [class.correct]="answer.isCorrect && selectedAnswer?.id === answer?.id"
        [class.with-image]="answer.image"
        #listItem
        (click)="!hasAnswered() && handleAnswerClick(currentQuestionIndex, answerIndex, answer)"
        (keydown.enter)="!hasAnswered() && handleAnswerClick(currentQuestionIndex, answerIndex, answer)"
      >
        <div class="answer-image-container" *ngIf="answer.image">
          <img class="answer-image" [src]="answer.image || placeholderImage" alt="Answer" loading="lazy" />
        </div>
        <div class="answer-content">
          <input class="radio-input" type="radio" [name]="'answer_' + currentQuestion?.id" [id]="'answer_' + currentQuestion?.id + '_' + answerIndex" />
          <label
            class="radio-label"
            [for]="'answer_' + currentQuestion?.id + '_' + answerIndex"
            [class.hidden-original-circle]="listItem.classList.contains('correct') || listItem.classList.contains('wrong')"
          >
            {{ answer.title }}
            <span class="extra-label correct"><i class="extra-label-icon icon"></i></span>
            <span class="extra-label wrong"><i class="extra-label-icon icon"></i></span>
          </label>
        </div>
      </div>
    </div>
  </div>

  <div class="sponsored-quiz-footer">
    <ng-container *ngIf="isNextButtonVisible && selectedAnswer?.id; else resultTemplate">
      <button [disabled]="!selectedAnswer" class="adocean-next-button" (click)="onGetNextQuestion()">
        {{ currentQuestionIndex + 1 === data?.questions?.length ? 'Eredmény megtekintése' : 'Következő kérdés' }}
      </button>
    </ng-container>

    <ng-template #resultTemplate>
      <div *ngIf="rating" class="sponsored-quiz-result">
        <a (click)="onFBShareClick()" class="sponsored-quiz-result-share">
          <i class="fb-icon icon icon-share-facebook"></i>
          Facebook megosztás
        </a>
      </div>
    </ng-template>
  </div>
</div>

<ng-template #ratingImage>
  <div class="sponsored-quiz-result-image-container">
    <img
      class="sponsored-quiz-result-image"
      [src]="rating?.thumbnailUrl || rating?.image?.fullSizeUrl || rating?.image?.thumbnailUrl || rating?.image || placeholderImage"
      alt="Quiz result"
      loading="lazy"
    />
  </div>
</ng-template>

<ng-template #ratingText>
  {{ rating?.text }}
  <a class="sponsored-quiz-result-more-info-link" *ngIf="data?.isMoreInfoLinkEnabled && data?.moreInfoLink" [href]="data?.moreInfoLink" target="_blank">
    Szeretne többet megtudni erről?
  </a>
</ng-template>
