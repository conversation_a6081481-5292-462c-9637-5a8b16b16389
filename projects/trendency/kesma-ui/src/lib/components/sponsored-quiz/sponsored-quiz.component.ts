import { CommonModule, NgForOf, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { QuizAnswer } from '../../definitions';
import { QuizComponent } from '../quiz/quiz.component';

@Component({
  selector: 'kesma-sponsored-quiz',
  templateUrl: './sponsored-quiz.component.html',
  styleUrls: ['./sponsored-quiz.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [NgForOf, NgIf, CommonModule],
})
export class SponsoredQuizComponent extends QuizComponent {
  currentQuestionIndex = 0;
  selectedAnswer?: QuizAnswer;

  public hasAnswered(): boolean {
    return this.givenAnswers[this.currentQuestionIndex] !== undefined;
  }

  public handleAnswerClick(questionIndex: number, answerIndex: number, answer: QuizAnswer): void {
    this.onSelectAnswer(questionIndex, answerIndex);
    this.onPickAnswer(answer);
  }

  get isNextButtonVisible(): boolean {
    return !!this.data?.questions?.length && this.data?.questions?.length > this.currentQuestionIndex;
  }

  onPickAnswer(answer: QuizAnswer): void {
    if (this.selectedAnswer) {
      return;
    }

    this.selectedAnswer = answer;
  }

  onGetNextQuestion(): void {
    this.selectedAnswer = undefined;
    this.currentQuestionIndex++;
    this.currentQuestion = this.data?.questions[this.currentQuestionIndex];
  }
}
