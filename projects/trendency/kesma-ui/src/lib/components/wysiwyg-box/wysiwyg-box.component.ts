import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostListener,
  inject,
  Input,
  OnChanges,
  OnInit,
  QueryList,
  Renderer2,
  SimpleChanges,
  ViewChildren,
} from '@angular/core';
import { EmbeddingService, RunScriptsDirective, UtilService } from '@trendency/kesma-core';
import { ImageLightbox } from '../image-lightbox/image-lightbox.definitions';
import { DOCUMENT, NgFor, NgIf } from '@angular/common';
import { BypassPipe } from '../../pipes';
import { ImageLightboxComponent } from '../image-lightbox/image-lightbox.component';
import { ArticleFileLinkDirective } from '../../directives';

const CHARACTER_LENGTH_FOR_QUOTE_ICON = 36;

@Component({
  selector: 'kesma-wysiwyg-box',
  templateUrl: './wysiwyg-box.component.html',
  styleUrls: ['./wysiwyg-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, ImageLightboxComponent, BypassPipe, ArticleFileLinkDirective, RunScriptsDirective],
})
export class WysiwygBoxComponent implements OnInit, AfterViewInit, OnChanges {
  @Input() public htmlArray: string[] = [];
  @Input() public html = '';
  @Input() public useLightbox = false;
  @Input() public useTableWrapper = false;
  @Input() public removeScriptsOnSsr = true;
  @ViewChildren('wysiwygelement', { read: ElementRef }) wsElements?: QueryList<ElementRef>;
  htmlBlocks: string[] = [];
  imageLightbox?: ImageLightbox | null;
  isLightboxVisible = false;
  isMobile = this.utilsService.isBrowser() ? window.innerWidth <= 768 : false;
  htmlBlocksCopy: string[] = [];
  private readonly document = inject(DOCUMENT);

  constructor(
    protected readonly embeddingService: EmbeddingService,
    protected readonly utilsService: UtilService,
    protected readonly renderer: Renderer2,
    protected cdr: ChangeDetectorRef
  ) {}

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.isMobile = this.utilsService.isBrowser() ? window.innerWidth <= 768 : false;
  }

  ngOnInit(): void {
    this.prepareHtmlBlocks();
  }

  ngAfterViewInit(): void {
    this.prepareElements();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (!changes?.['html']?.firstChange || !changes?.['htmlArray']?.firstChange) {
      this.prepareHtmlBlocks();
      this.prepareElements();
    }
  }

  onCloseLightbox(): void {
    this.imageLightbox = null;
  }

  protected prepareHtmlBlocks(): void {
    this.htmlBlocks = (this.htmlArray?.length ? this.htmlArray : [this.html ?? '']).map((htmlContent) =>
      this.embeddingService.deferIframeLoad(htmlContent, { useNativeLazyLoading: true })
    );
  }

  protected prepareElements(): void {
    (this.wsElements ?? []).forEach((element: ElementRef<HTMLElement>) => {
      const oembedElements = element.nativeElement.getElementsByTagName('oembed');
      if (oembedElements.length > 0 && this.utilsService.isBrowser() && JSON.stringify(this.htmlBlocks) !== JSON.stringify(this.htmlBlocksCopy)) {
        this.embeddingService.createAnchorForEmbedly(Array.from(oembedElements));
        this.cdr.detectChanges();
      }

      this.htmlBlocksCopy = this.htmlBlocks;

      const figureElements = element.nativeElement.getElementsByTagName('figure');
      if (figureElements?.length && this.utilsService.isBrowser()) {
        for (let i = 0; i < figureElements.length; i++) {
          this.renderer.listen(figureElements[i]?.children[0], 'click', () => {
            if (figureElements[i].querySelector('table')) {
              return;
            }
            const url = (figureElements[i]?.children as any)[0]?.src ?? '';
            const caption = (figureElements[i]?.children as any)[1]?.innerText?.split('\n')?.[0];
            const source = (figureElements[i]?.children as any)[1]?.innerText?.split('\n')?.[1];
            this.imageLightbox = { url, caption, source };
            this.isLightboxVisible = true;
            this.cdr.markForCheck();
          });
        }
      }
    });
    this.embeddingService.loadEmbedMedia((this.wsElements ?? []).map(({ nativeElement }) => nativeElement).filter((e) => !!e));
    this.programaticallyAddingQuoteIcon();

    this.wsElements?.map((element) => {
      const tables = Array.from(element.nativeElement?.querySelectorAll('figure.table')) as HTMLTableElement[];
      tables.map((tableFigure) => {
        const float = tableFigure.style.float;
        if (float === 'left') {
          tableFigure.classList.add('table-style-align-left');
          tableFigure.classList.remove('table-style-align-right');
          return;
        }
        if (float === 'right') {
          tableFigure.classList.add('table-style-align-right');
          tableFigure.classList.remove('table-style-align-left');
          return;
        }
        tableFigure.classList.remove('table-style-align-left');
        tableFigure.classList.remove('table-style-align-right');
      });
    });

    if (this.useTableWrapper) {
      this.tableWrapper();
    }
  }

  protected tableWrapper(): void {
    this.wsElements?.map((element) => {
      const tables = Array.from(
        element.nativeElement?.querySelectorAll(':not(.table-wrapper) > figure.table, :not(table-wrapper) > :not(figure) > table')
      ) as HTMLTableElement[];
      tables.map((table) => {
        if (!table.parentNode) {
          return;
        }
        const wrapperElement = this.document.createElement('div');
        const wrapperClasses = ['table-wrapper', ...Array.from(table.classList).filter((className) => className !== 'table')];
        wrapperElement.classList.add(...wrapperClasses);
        table.parentNode.insertBefore(wrapperElement, table);
        wrapperElement.appendChild(table);
      });
    });
  }

  protected programaticallyAddingQuoteIcon(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }
    const foundElements = Array.from(this.document.getElementsByClassName('quote'));
    if (foundElements?.length < 1) {
      return;
    }

    foundElements.forEach((item: Element) => {
      const textLength = item?.innerHTML?.length;

      if (textLength > CHARACTER_LENGTH_FOR_QUOTE_ICON) {
        item.classList.add('quote-logo');
      }
    });
  }
}
