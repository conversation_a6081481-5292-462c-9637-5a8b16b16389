import { Component, ChangeDetectionStrategy, input, effect, OnInit, OnDestroy, computed, signal, viewChild, ElementRef } from '@angular/core';
import { Chart, ChartData } from 'chart.js/auto';
import { ChartType } from 'chart.js/dist/types';

const DEFAULT_TYPE = 'bar';

@Component({
  selector: 'kesma-chart',
  templateUrl: './chart.component.html',
  styleUrl: './chart.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ChartComponent implements OnInit, OnDestroy {
  readonly data = input.required<(ChartData & { type?: string }) | string>();

  readonly transformedData = computed<ChartData & { type?: string }>(() => {
    const data = this.data();
    if (typeof data === 'string') {
      try {
        return JSON.parse(data);
      } catch {
        return null;
      }
    }
    return data;
  });

  readonly canvas = viewChild<ElementRef>('chartCanvas');

  chart: Chart | null = null;

  constructor() {
    effect(() => {
      if (this.chart) {
        const { labels, datasets, type } = this.transformedData();
        const { type: currentType } = this.chart.getContext();
        if (currentType !== type) {
          this.chart.destroy();
          this.createChart(type as ChartType);
        }
        this.chart.data.labels = labels;
        this.chart.data.datasets = datasets;
        this.chart.update();
      }
    });
  }

  ngOnInit(): void {
    this.createChart();
  }

  ngOnDestroy(): void {
    if (!this.chart) {
      return;
    }
    this.chart.destroy();
  }

  private createChart(chartType: ChartType = DEFAULT_TYPE): void {
    this.chart = new Chart(this.canvas()?.nativeElement.getContext('2d'), {
      type: chartType,
      data: {
        labels: [],
        datasets: [],
      },
      options: {
        responsive: true,
        aspectRatio: 16 / 9,
      },
    });
  }
}
