<div class="real-estate-wrapper" *ngIf="data?.length">
  <ng-container *ngIf="showHeader">
    <div class="real-estate-wrapper-header">
      <img class="real-estate-wrapper-header-logo" loading="lazy" src="/assets/images/ingatlanbazar-header.svg" alt="Ingatlanbazár logo" />
    </div>
    <hr />
  </ng-container>

  <div *ngIf="activeRealEstates?.length" class="real-estate-box" #realEstateBox [ngClass]="{ multiple: activeRealEstates.length > 1 }">
    <div *ngIf="data?.length ?? 0 > itemsToShow" (click)="prev($event)" [class.small]="isSmallBox()" class="real-estate-box-arrow-box left">
      <img class="arrow left" loading="lazy" src="/assets/images/ingatlanbazar-arrow.svg" alt="Ingatlanbazár nyíl balra" />
    </div>

    <div *ngIf="data?.length ?? 0 > itemsToShow" (click)="next($event)" [class.small]="isSmallBox()" class="real-estate-box-arrow-box right">
      <img class="arrow right" loading="lazy" src="/assets/images/ingatlanbazar-arrow.svg" alt="Ingatlanbazár nyíl jobbra" />
    </div>
    <a *ngFor="let element of activeRealEstates" [href]="'https://ingatlanbazar.hu' + element?.seoUrl" class="real-estate-element" target="_blank">
      <div *ngIf="element" class="real-estate-element-image-wrapper">
        <img [class.small]="isSmallBox()" loading="lazy" src="{{ element.pictureSrc }}" alt="Ingatlan kép" />
      </div>
      <div *ngIf="element" [class.small]="isSmallBox()" class="real-estate-element-data-wrapper">
        <h3 class="price">{{ element.priceHUF / 1000000 | number: '1.0-1' }}{{ ' M Ft' }}</h3>
        <h4 class="data-name">
          Szobák száma: <span class="data-value">{{ element?.roomsText + ' szoba' }}</span>
        </h4>
        <h4 class="data-name">
          Mérete: <span class="data-value">{{ element?.area + ' m' }}<sup>2</sup> </span>
        </h4>
        <h4 class="data-name">
          Elhelyezkedés: <span class="data-value">{{ element?.settlement }}</span>
        </h4>
      </div>
    </a>
  </div>
  <div class="more-estates">
    <a class="more-estates-button" [href]="moreRealEstatesLink" target="_blank"> Még több ingatlan </a>
  </div>
</div>
