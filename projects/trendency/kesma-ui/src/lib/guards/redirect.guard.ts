import { CanActivateFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { catchError, map } from 'rxjs/operators';
import { ReqService, SeoService } from '@trendency/kesma-core';
import { RedirectService } from '../services';
import { of } from 'rxjs';

export const CheckRedirectBefore404Guard: CanActivateFn = () => {
  const redirectService = inject(RedirectService);
  const reqService = inject(ReqService);
  const seoService = inject(SeoService);
  const router = inject(Router);
  if (router.navigated) {
    // This is necessary because if we click on a 404 content within the site — whether it's an author, article, or anything else —
    // we should not call the redirect API. Because it might happen that the API responds, and instead of a valid 404,
    // we would end up redirecting the user.
    return true;
  }
  return reqService.get<{ url: string }>(`/portal/redirection?url=${seoService.currentUrl}`).pipe(
    map(({ url }) => {
      if (url) {
        redirectService.redirectOldUrl(encodeURI(url), true);
        return false;
      }
      return true;
    }),
    catchError(() => {
      return of(true);
    })
  );
};
